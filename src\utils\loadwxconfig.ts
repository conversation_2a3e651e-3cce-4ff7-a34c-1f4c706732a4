import yunyingService from '@/service/yunyingService'

export const loadWxConfig = () => {
    yunyingService
        .microsrvWechatJssign({
            appid: import.meta.env.VITE_APP_WECHAT_APPID,
            url: window.location.href,
        })
        .then((res) => {
            if (res && res.code === 0) {
                window.wx.config({
                    debug: false,
                    // 公众号唯一标识
                    appId: res.result.appId,
                    // 服务端生成的时间戳
                    timestamp: res.result.timestamp,
                    // 服务端生成的随机字符串
                    nonceStr: res.result.nonceStr,
                    // 服务端生成的签名
                    signature: res.result.signature,
                    // 配置开放js标签列表 随便开通一个
                    jsApiList: [],
                    // 配置开放标签列表 开通跳转小程序标签
                    openTagList: ['wx-open-launch-weapp'],
                })

                window.wx.error(function (res) {
                    console.log('error ' + JSON.stringify(res))
                })

                window.wx.ready(() => {
                    console.log('ready')
                })
            }
        })
}
