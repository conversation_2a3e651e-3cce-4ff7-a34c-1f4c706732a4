<template>
    <div class="wrapper flex flex-column row-gap-10 b-margin-10 font-16">
        <div
            v-if="row['pid'] || (row['personRelatedEntNum'] && Number(row['personRelatedEntNum']))"
            style="color: var(--main-blue-)"
            @click="$emit('toCompanyDetail', row)"
        >
            {{ ENTNAME?.key && row[ENTNAME?.key] }}
        </div>
        <div v-else>
            {{ ENTNAME?.key && row[ENTNAME?.key] }}
        </div>
        <ColumnItems
            class="flex font-16 lh-18 gap-10"
            :columns="others || []"
            :row="row"
            :channelType="channelType"
            :modelName="modelName"
            :page_config="page_config"
        />
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { RequestKeys, type PageConfigItem } from '../../config'
import ColumnItems from './ColumnItems.vue'

const { page_config, row, channelType, modelName } = defineProps<{
    page_config: PageConfigItem
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    row: any
    channelType: number
    modelName: RequestKeys
}>()

const companyKey = ['ENTNAME', 'INV']
const ENTNAME = computed(() => {
    return page_config.columns?.filter((item) => companyKey.includes(item.key))?.[0]
})

const others = computed(() => {
    return page_config.columns?.filter((item) => !companyKey.includes(item.key))
})
</script>

<style scoped lang="scss">
.change-wrapper {
    position: relative;
    box-sizing: border-box;
    transition: max-height 0.3s ease;
    position: relative;
    &::before {
        content: ' ';
        position: absolute;
        height: 100%;
        width: 1px;
        left: 50%;
        top: 0;
        background-color: #e5e5e5;
    }
}
.expand {
    padding: 10px;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    box-sizing: border-box;
}
.mask {
    position: absolute;
    top: -20px;
    left: 0;
    width: 100%;
    height: 40px;
    background: linear-gradient(to top, #f6f7fc 0%, transparent 100%);
    pointer-events: none; /* 让点击事件穿透遮罩 */
}
</style>
