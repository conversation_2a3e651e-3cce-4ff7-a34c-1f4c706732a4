<template>
    <div class="tb-padding-12 lr-padding-16 border-box" style="background-color: #f9f9f9; height: 100vh">
        <div class="display-flex flex-column space-between lr-padding-16 background-card"
             :style="{ backgroundImage: `url(${globalInfo.backgroundImage})`, color: `${globalInfo.titleColor}` }"
             style="background-repeat: no-repeat">
            <div class="flex-1">
                <div class="display-flex top-bottom-center space-between t-padding-12">
                    <div class="display-flex top-bottom-center gap-4 b-margin-4">
                        <Icon :icon="globalInfo.icon" size="20" :color="globalInfo.fontColor" />
                        <span class="font-16 font-weight-500">{{ globalInfo.title }}</span>
                    </div>
                    <van-popover v-model:show="showPopover" :actions="actions" @select="onSelect"
                                 class="custom-popover">
                        <template #reference>
                            <div class="display-flex top-bottom-center">
                                <span class="font-14" style="margin-right: -2px">切换</span>
                                <Icon v-if="!showPopover" icon="icon-a-Frame51" size="20"
                                      :color="globalInfo.fontColor" />
                                <Icon v-if="showPopover" icon="icon-a-Frame511" size="20"
                                      :color="globalInfo.fontColor" />
                            </div>
                        </template>
                    </van-popover>
                </div>
                <div class="t-margin-4 font-12" :class="{ color: `${globalInfo.fontColor}` }" style="width: 60%">
                    {{ globalInfo.content }}
                </div>
            </div>
            <div class="b-padding-12">
                <span class="font-12" :class="{ color: `${globalInfo.fontColor}` }">可用额度：</span>
                <span class="font-20">{{ account }}</span>
            </div>
        </div>
        <div class="t-margin-12">
            <div class="display-flex gap-8">
                <button class="status-btn" :class="{ active: selectedStatus === 'available' }"
                        @click="onClickStatus('available')">
                    未过期
                </button>
                <button class="status-btn" :class="{ active: selectedStatus === 'expired' }"
                        @click="onClickStatus('expired')">
                    已过期
                </button>
            </div>
        </div>
        <div class="t-margin-12">
            <van-list v-model:loading="loading" :finished="finished" finished-text="暂无数据" @load="onLoad">
                <van-cell v-for="item in benefitList" :key="item.id">
                    <div class="display-flex top-bottom-center tb-padding-12 border-box" style="height: 2rem">
                        <div class="flex-1 display-flex flex-column top-bottom-center" style="align-items: flex-start">
                            <div class="display-flex font-14 color-black" style="width: 100%">
                                <div class="display-flex top-bottom-center">
                                    <Icon :icon="globalInfo.icon" class="r-margin-4" size="16" />
                                    {{ serviceName(item.serviceKey) }}
                                    <van-tag v-if="item.status" class="l-margin-4" color="#F2FBF8" text-color="#00B273"
                                             size="medium" style="border: 1px solid #00b273">
                                        <span class="font-12">未过期</span>
                                    </van-tag>
                                    <van-tag v-else class="l-margin-4" color="#FEF6F5" text-color="#E95133"
                                             size="medium" style="border: 1px solid #e95133">
                                        <span class="font-12">已过期</span>
                                    </van-tag>
                                </div>
                                <div class="flex-1 display-flex justify-end">
                                    <button v-if="!item.status || item.remainingAmount === 0" class="nogift-btn">
                                        无法赠送
                                    </button>
                                    <button v-else class="gift-btn" @click="giveToFriend(item)">赠送好友</button>
                                </div>
                            </div>
                            <span class="font-12 color-two-gray" style="margin-bottom: -10px">
                                购买时间：{{
                                    item.createTime ? moment(item.createTime).format('YYYY-MM-DD HH:mm:ss') : '-'
                                }}
                            </span>
                            <div class="display-flex space-between top-bottom-center font-12 color-two-gray"
                                 style="width: 100%" @click="toBenefitUsage(item.transId)">
                                <span>
                                    有效时间：{{
                                        item.endTime ? moment(item.endTime).format('YYYY-MM-DD HH:mm:ss') : '-'
                                    }}
                                </span>
                                <div class="display-flex top-bottom-center font-12 color-two-gray">
                                    {{ item.remainingAmount }}/{{ item.totalAmount }}
                                    <Icon icon="icon-a-Frame51" size="16" color="#B3B3B3" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- <div v-if="item.isGifted" style="border: 1px solid #F2F2F2;"></div>
                    <div v-if="item.isGifted" class="display-flex font-12 t-margin-4" style="color: #FF854C;align-items: flex-start;" >来自{{ item.from }}的赠送</div> -->
                </van-cell>
            </van-list>
        </div>
    </div>
    <van-overlay :show="showBenefit" z-index="100">
        <div class="flex-center" style="height: 18rem">
            <div style="width: 100vw; height: 55%">
                <div class="benefit-card">
                    <div class="display-flex flex-column relative" style="top: 1.68rem; left: 2.1rem">
                        <span class="font-16 b-margin-4" style="color: #e6eeff">尊享权益卡</span>
                        <span class="font-12 b-margin-4" style="color: rgba(230, 238, 255, 0.5)">
                            一旦赠送，将无法撤销
                        </span>
                        <span class="font-12" style="color: #e6eeff">
                            设置赠送份数
                            <div class="stepper-wrapper l-margin-50">
                                <van-stepper class="custom-stepper" v-model="sendCount" :max="maxCount" />
                            </div>
                        </span>
                    </div>
                    <div class="display-flex left-right-center relative font-14 color-black font-weight-500"
                         style="top: 3.4rem">
                        赠送“{{ globalInfo.name }}”权益
                    </div>
                    <div class="display-flex left-right-center relative gap-8" style="top: 3.6rem">
                        <div class="flex-center t-margin-6" style="
                                width: 0.8rem;
                                height: 0.8rem;
                                border-radius: 50%;
                                background: linear-gradient(to right, #3c74eb, #95d5f4);
                            ">
                            <Icon :icon="globalInfo.icon" color="#FFFFFF" size="20" />
                        </div>
                        <div class="font-12 color-two-grey display-flex flex-column gap-4">
                            <span>
                                可赠送权益：
                                <span class="color-black">{{ maxCount }}</span>
                            </span>
                            <span>
                                权益有效期：
                                <span class="color-black">{{ selectedDueTime ? selectedDueTime : '-' }}</span>
                            </span>
                        </div>
                    </div>
                    <div class="display-flex left-right-center relative" style="top: 4.45rem">
                        <span class="font-10 color-three-grey">若24h内无人领取会自动退回原账户</span>
                    </div>
                    <div class="display-flex left-right-center relative" style="top: 4.5rem">
                        <button id="send-btn" class="btn border-radius-16"
                                style="width: 6rem; height: 0.8rem; background: linear-gradient(to right, #3c74eb, #95d5f4)"
                                @click="handleSendBenefit">
                            赠送好友
                        </button>
                    </div>
                </div>
                <div class="flex-center" style="width: 100%; height: 1.3rem">
                    <Icon icon="icon-a-Frame1171276285" @click="showBenefit = false" size="20"
                          color="rgba(255, 255, 255, 0.7)" />
                </div>
            </div>
        </div>
    </van-overlay>
    <van-popup v-model:show="showCopy" position="bottom" :style="{ height: '6%' }" z-index="999" class="flex-center">
        <button ref="copyButton" class="border-radius-16 font-16 btn" style="width: 98%; height: 90%"
                @click="copyUrl(claimUrl)">
            复制链接
        </button>
    </van-popup>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onBeforeMount, getCurrentInstance } from 'vue'
import { useRoute, useRouter } from 'vue-router'
// import { useStore } from 'vuex'
// import type { RootState } from '@/types/store'
import orderService from '@/service/orderService'
import Icon from '@/components/common/Icon.vue'
import csMxImg from '@/assets/images/my/cs-mx.png'
import fpMxImg from '@/assets/images/my/fp-mx.png'
import lxfsImg from '@/assets/images/my/lxfs-mx.png'
import type { IServiceOrderPageParams, IServiceOrderResponseItem } from '@/types/order'
// import { copyToClipboard } from '@/utils/copy'
import { showToast } from 'vant'

const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment
// const store = useStore<RootState>()
// const user = computed(() => {
//     const { account } = store.state.user
//     const { user } = account || {}
//     return user
// })
const showCopy = ref<boolean>(false)
const serviceName = (key: string) => {
    if (key === 'xs') {
        return '线索联系方式'
    } else if (key === 'swbg') {
        return '企业财税经营分析报告'
    } else if (key === 'gqbg') {
        return '高新技术科技企业报告'
    } else if (key === 'fpbg') {
        return '企业发票数据综合分析报告'
    } else if (key === 'znwh') {
        return '智能外呼'
    } else {
        return '-'
    }
}
const router = useRouter()
const route = useRoute()
const showPopover = ref(false)
const actions = [{ text: '联系方式' }, { text: '财税报告' }, { text: '发票报告' }]

const type = ref('xs')
const selectedStatus = ref('')

const globalInfo = computed(() => {
    const styleMap: Record<
        string,
        {
            title: string
            name: string
            content: string
            backgroundImage: string
            titleColor: string
            fontColor: string
            icon: string
        }
    > = {
        xs: {
            title: '联系方式',
            name: '联系方式',
            content: '可查看企业工作人员的电话及邮箱',
            backgroundImage: lxfsImg,
            titleColor: '#3C74EB',
            fontColor: '#779EF1',
            icon: 'icon-a-Frame11712762191',
        },
        swbg: {
            title: '财税报告',
            name: '企业财税经营分析报告',
            content: '深入分析企业核心财务指标，直观剖析企业经营稳健度',
            backgroundImage: csMxImg,
            titleColor: '#F98A35',
            fontColor: '#FBAD72',
            icon: 'icon-Frame3',
        },
        fpbg: {
            title: '发票报告',
            name: '企业发票数据综合分析报告',
            content: '分析企业涉票行为、交易架构，挖掘潜在风险',
            backgroundImage: fpMxImg,
            titleColor: '#28C1FF',
            fontColor: '#53CDFF',
            icon: 'icon-Frame1',
        },
    }
    return styleMap[type.value] || { backgroundImage: lxfsImg, fontColor: '#3C74EB' }
})

// 列表
const benefitList = ref<IServiceOrderResponseItem[]>([])
// 选中可赠送的最大权益数量
const maxCount = ref<number>()
// 选中权益的到期时间
const selectedDueTime = ref<string>()
const showBenefit = ref<boolean>(false)

const sendCount = ref(1)
const account = ref<number>()
const loading = ref<boolean>(false)
const finished = ref<boolean>(false)

const toBenefitUsage = (id: string) => {
    console.log(id)
    router.push({
        path: '/my/benefit/usage-record',
        query: {
            transId: id,
        },
    })
}

const onSelect = (index: { text: string }) => {
    selectedStatus.value = ''
    if (index.text === '联系方式') {
        type.value = 'xs'
    }
    if (index.text === '财税报告') {
        type.value = 'swbg'
    }
    if (index.text === '发票报告') {
        type.value = 'fpbg'
    }
    queryParams.value.page = 1
    queryParams.value.serviceKey = type.value
    benefitList.value = []
    orderService.orderServiceStatistics({ serviceKeys: type.value }).then((res) => {
        console.log(res)
        account.value = res[0].num
    })
    onLoad()
}

const onClickStatus = (status: string) => {
    queryParams.value.page = 1
    benefitList.value = []
    let params = {
        page: 1,
        pageSize: 10,
        serviceKey: type.value,
    } as IServiceOrderPageParams

    if (selectedStatus.value === status) {
        // 如果已经选中该状态，再次点击则清除选中状态
        selectedStatus.value = ''
        params = {
            page: 1,
            pageSize: 10,
            serviceKey: type.value,
        }
    } else {
        // 选中该状态
        selectedStatus.value = status
        if (status === 'available') {
            params = {
                page: 1,
                pageSize: 10,
                serviceKey: type.value,
                status: 'NORMAL',
            }
        } else if (status === 'expired') {
            params = {
                page: 1,
                pageSize: 10,
                serviceKey: type.value,
                status: 'EXPIRED',
            }
        }
    }
    queryParams.value = params
    onLoad()
}

const selectItem = ref<IServiceOrderResponseItem>()

const giveToFriend = (item: IServiceOrderResponseItem) => {
    maxCount.value = item.remainingAmount
    showBenefit.value = true
    selectedDueTime.value = moment(item.endTime).format('YYYY-MM-DD HH:mm:ss')
    selectItem.value = item
}
const copyButton = ref<HTMLButtonElement | null>(null)

const copyUrl = (url: string) => {
    navigator.clipboard
        .writeText(url)
        .then(function () {
            showToast('复制成功')
            showCopy.value = false
        })
        .catch(function (err) {
            showToast(`无法复制文本:${err}`)
            console.error()
        })
}

const queryParams = ref<IServiceOrderPageParams>({
    page: 1,
    pageSize: 10,
    serviceKey: type.value,
})

const search = async (params: IServiceOrderPageParams) => {
    const res = await orderService.orderServiceOrderPage(params)
    benefitList.value.push(...res.data)
    return res
}

let ti = null
const onLoad = () => {
    if (ti) {
        clearTimeout(ti)
    }
    ti = setTimeout(async () => {
        const res = await search(queryParams.value)
        queryParams.value.page += 1
        loading.value = false
        console.log('benefitList.value.length', benefitList.value.length)
        if (benefitList.value.length === res.total) {
            finished.value = true
        }
    }, 100)
}

onBeforeMount(() => {
    type.value = (route.query.type as string) || 'xs'
    queryParams.value.serviceKey = type.value
})
const claimUrl = ref('')
const handleSendBenefit = async () => {
    if (selectItem.value) {
        const orderPredeductParams = {
            transId: selectItem.value.transId,
            prePayAmount: sendCount.value,
            isGift: 1,
        }
        const res = await orderService.orderPrededuct(orderPredeductParams)
        if (res.success) {
            console.log('res.data', res.data.id)
            const { id } = res.data
            const { uuid } = res.data
            claimUrl.value = `${window.location.origin}/claim-benefit?id=${id}&uuid=${uuid}`
            showCopy.value = true
            showBenefit.value = false
        } else {
            showToast({
                message: res.errMsg,
                type: 'fail',
            })
        }
    } else {
        showToast({
            message: '权益信息不存在',
            type: 'fail',
        })
    }
}

// watch(showBenefit, (newValue) => {
//     if (newValue) {
//         nextTick(() => {
//             const sendBtn = document.getElementById('send-btn')
//             if (sendBtn) {
//                 sendBtn.addEventListener('click',async () => {
//                     await handleSendBenefit()
//                     await navigator.clipboard.writeText(claimUrl.value).then(function () {
//                         showToast('复制成功')
//                         showCopy.value = false
//                     }).catch(function (err) {
//                         showToast(`无法复制文本:${err}`)
//                         console.error()
//                     })
//                 })
//             } else {
//                 console.error('元素 send-btn 未找到')
//             }
//         })
//     } else {
//         const sendBtn = document.getElementById('send-btn')
//         if (sendBtn) {
//             sendBtn.addEventListener('click',async () => {
//                 await handleSendBenefit()
//                 await navigator.clipboard.writeText(claimUrl.value).then(function () {
//                     showToast('复制成功')
//                     showCopy.value = false
//                 }).catch(function (err) {
//                     showToast(`无法复制文本:${err}`)
//                     console.error()
//                 })
//             })
//         }
//     }
// })

onMounted(async () => {
    orderService.orderServiceStatistics({ serviceKeys: type.value }).then((res) => {
        console.log(res)
        account.value = res[0].num
    })
})
</script>

<style lang="scss" scoped>
.background-card {
    height: 3.6rem;
    background-size: contain;
    background-position: center;
    border-radius: 8px;
    overflow: hidden;
}

.status-btn {
    width: 2rem;
    height: 0.8rem;
    border: 1px solid #e5e5e5;
    background-color: #ffffff;
    border-radius: 4px;
    font-size: 14px;

    &.active {
        border-color: #3c74eb;
        background-color: #e5edff;
        color: #3c74eb;
    }
}

.gift-btn {
    width: 2rem;
    height: 0.8rem;
    border: none;
    border-radius: 8px;
    background: linear-gradient(to right, #3c74eb, #95d5f4);
    color: white;
    font-size: 12px;
}

.nogift-btn {
    width: 2rem;
    height: 0.8rem;
    border: none;
    border-radius: 8px;
    background: #f9f9f9;
    color: #999999;
    font-size: 12px;
}

:deep(.van-cell) {
    margin-bottom: 8px;
    border-radius: 8px;
}

.custom-popover {
    :deep(.van-popover__action) {
        width: 2rem !important;
        height: 0.4rem !important;
        min-height: 26px !important;
        line-height: 26px !important;
        background: transparent !important;
        font-size: 14px !important;
        text-align: left !important;
        padding: 0 8px !important;
        box-sizing: border-box !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        border: none !important;
        box-shadow: none !important;
    }
}

.benefit-card {
    height: 100%;
    background-image: url('@/assets/images/my/benefit-card.png');
    background-size: 7.6rem 9.2rem;
    background-position: center;
    background-repeat: no-repeat;
}

.stepper-wrapper {
    border: 1px solid white;
    border-radius: 8px;
    display: inline-block;
}

.custom-stepper {
    :deep(.van-stepper) {
        background: transparent !important;
        border: 1px solid white !important;
        border-radius: 4px !important;
    }

    :deep(.van-stepper__minus),
    :deep(.van-stepper__plus) {
        background: transparent !important;
        border: none !important;
        color: white !important;
    }

    :deep(.van-stepper__input) {
        background: transparent !important;
        border: none !important;
        color: white !important;
    }
}
</style>
