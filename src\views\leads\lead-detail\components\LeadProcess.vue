<script lang="ts" setup>
import crmService from '@/service/crmService'
import type { ILeadData } from '@/types/lead'
import { showConfirmDialog } from 'vant'
import { ref } from 'vue'

const props = defineProps<{
    data: ILeadData
}>()

const statusRef = ref(props.data.status)

const list = [
    {
        id: 1,
        title: '01.初访',
        value: 1,
    },
    {
        id: 2,
        title: '02.意向',
        value: 2,
    },
    {
        id: 3,
        title: '03.报价',
        value: 3,
    },
    {
        id: 4,
        title: '04.搁置',
        value: 5,
    },
    {
        id: 5,
        title: '05.成交',
        value: 4,
    },
]

const doUpdateProcessStatus = (status: number) => {
    crmService
        .crmUpdateStatus({
            leadId: props.data.id,
            status: status,
        })
        .then((res) => {
            if (res) {
                statusRef.value = status
            }
        })
}

const updateProcessStatus = (status: number) => {
    if (statusRef.value === 4) return
    if (status === 4) {
        showConfirmDialog({
            title: '转为成交客户',
            message: `是否确认该客户转为成交客户？`,
        })
            .then(() => {
                doUpdateProcessStatus(status)
            })
            .catch(() => {})
    } else {
        doUpdateProcessStatus(status)
    }
}
const isActive = (index: number) => {
    const targetIndex = list.findIndex((e) => e.value === statusRef.value)

    return index <= targetIndex
}
</script>
<template>
    <div class="flex flex-column gap-6">
        <div class="font-14 color-black font-weight-500">客户成交流程</div>
        <div class="flex flex-row gap-8 oa">
            <div
                v-for="(item, index) in list"
                :key="item.value"
                :class="`${isActive(index) ? 'active-btn' : 'unactive-btn'} flex h-51 w-100 mw-100 back-color-blue relative center l-padding-12`"
                :style="`z-index:${list.length - index}`"
                @click="updateProcessStatus(item.value)"
            >
                <div class="triangle"></div>
                <div class="left-triangle"></div>
                <div class="font-14 font-weight-500">{{ item.title }}</div>
            </div>
        </div>
    </div>
</template>
<style scoped lang="scss">
.active-btn {
    border-top: 1px solid var(--main-blue-);
    border-bottom: 1px solid var(--main-blue-);
    background-color: var(--main-blue-);
    color: white;
}

.unactive-btn {
    border-top: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
    background-color: white;
    color: var(--main-black);
}

.triangle {
    position: absolute;
    right: -18px;
    top: 7px;
    background-color: var(--main-blue-);
    height: 36px;
    width: 36px;
    transform: rotate(45deg);
}

.left-triangle {
    position: absolute;
    left: -18px;
    top: 7px;
    background-color: white;
    height: 36px;
    width: 36px;
    transform: rotate(45deg);
}

.unactive-btn .triangle {
    background-color: white;
    border-top: 1px solid var(--border-color);
    border-right: 1px solid var(--border-color);
}

.unactive-btn .left-triangle {
    background-color: white;
    border-top: 1px solid var(--border-color);
    border-right: 1px solid var(--border-color);
}
</style>
