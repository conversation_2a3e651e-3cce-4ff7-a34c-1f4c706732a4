<template>
    <div class="home tb-padding-12 lr-padding-16 border-box" style="background-color: #F2F5F8">
        <div class="display-flex space-between align-center font-16 b-margin-12">
            <span class="font-weight-600">{{ companyName }}</span>
            <span class="">共
                <span class="color-blue">{{ totolNum }}</span>
                条
            </span>
        </div>
        <van-list v-model:loading="loading" :finished="finished" finished-text="暂无数据" @load="onLoad">
            <div v-if="dataList && dataList.length > 0">
                <van-cell v-for="(item,index) in dataList" :key="index" >
                    <div style="text-align: left;">
                        <div class="display-flex top-bottom-center gap-4">
                            <div style="height: 0.2rem; width: 0.2rem; border-radius: 50%; border: 2px solid #2b83fd"></div>
                            <div class="font-16 color-black font-weight-500">{{ moment(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
                            <div v-if="item.followType" class="lr-padding-8 border-radius-4 l-margin-8" style="background-color: #F2F5F8;border:1px solid #f2f2f2;">
                                <span class="font-14" style="color:#666666">{{ item.followType }}</span> 
                            </div>
                        </div>
                        <div class="tb-margin-4 l-margin-16 color-black" v-if="item.description" >
                            {{ item.description }}
                        </div>
                        <div class="l-margin-16 display-flex top-bottom-center">
                            <div class="font-16">
                                来自{{ item.referType == 'lead' ? '线索' : item.referType == 'customer' ? '客户' : '-' }}:{{ item.referName }}
                            </div>
                        </div>
                        <!-- 图片 -->
                        <div v-if="item.followImg && item.followImg.length > 0" class="flex-wrap display-flex top-bottom-center gap-4 t-margin-12">
                            <div v-for="(img,index) in item.followImg" :key="index">
                                <img
                                    :src="fileService.getFileUrl(img.name)"
                                    alt=""
                                    class="w-50 h-50"
                                    @click="previewImg(img.name)"
                                />
                            </div>
                        </div>
                    </div>
                </van-cell>
            </div>
            <div v-else class="t-margin-50 flex-center">
                <img src="@/assets/images/points/no-benefit-bg.png" alt="" style="width: 5rem; height: 5rem;">
            </div>
        </van-list>
    </div>
    <van-overlay :show="showImg" @click="showImg = false">
        <div class="wrapper">
            <!-- <div class="block" @click.stop ></div> -->
            <img :src="fileService.getFileUrl(currentImg)" alt="" class="w-300" />
        </div>
    </van-overlay>
</template>

<script lang='ts' setup>
import crmService from '@/service/crmService'
import fileService from '@/utils/fileService'
import { ref, computed, onMounted, getCurrentInstance } from 'vue'
import type { ICrmGetActiviesParams, ICrmGetActiviesItem } from '@/types/lead'
import { tabbarheight } from '@/utils/tabbar-height'
import { useRoute } from 'vue-router'

const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment
const route = useRoute()
const paddingBottom = computed(() => {
    return tabbarheight() + 'px'
})

const totolNum = ref(0)
const companyName = ref(route.query.companyName || '-')

const showImg = ref<boolean>(false)
const currentImg = ref<string>('')
const previewImg = (img:string) => {
    console.log('previewImg',img)
    currentImg.value = img
    showImg.value = true
}
const loading = ref<boolean>(false)
const finished = ref<boolean>(false)
const dataList = ref<ICrmGetActiviesItem[]>([])
const queryParams = ref<ICrmGetActiviesParams>({
    leadId: route.query.leadId as string,
    page:1,
    pageSize:10,
})
const search = async (params: ICrmGetActiviesParams) => {
    const res = await crmService.crmGetActivities(params)
    totolNum.value = res.total
    dataList.value.push(...res.data)
    return res
}
let ti: ReturnType<typeof setTimeout> | null = null
const onLoad = () => {
    if (ti) {
        clearTimeout(ti)
    }
    ti = setTimeout(async () => {
        await search(queryParams.value)
        queryParams.value.page += 1
        loading.value = false
        // console.log('benefitList.value.length',dataList.value.length)
        finished.value = true
    },100)
}

onMounted(() => {
    console.log('router',route.query)
})
</script>

<style lang='scss' scoped>
.home {
    height: 100%;
    overflow: scroll;
    padding-bottom: v-bind(paddingBottom);
    display: flex;
    flex-direction: column;
}

.wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}

:deep(.van-cell) {
    margin-bottom: 8px;
    border-radius: 8px;
}
</style>