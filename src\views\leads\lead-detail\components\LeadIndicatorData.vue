<script lang="ts" setup>
import indicatorService from '@/service/indicatorService'
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'

const props = defineProps<{
    socialCreditCode: string
    name: string
}>()

const loading = ref(false)
const gsInfoIndicatorData = ref<{ [key: string]: number }>({})
const router = useRouter()

const getBaseGsIndicatorData = () => {
    loading.value = true
    indicatorService
        .getCompareIndicatorData({ ids: '8034,122', socialCreditCode: props.socialCreditCode })
        .then((res) => {
            gsInfoIndicatorData.value = res
            console.log(res)
        })
        .finally(() => {
            loading.value = false
        })
}

const getTargetVal = (id: string): null | string | number => {
    let res = Number(gsInfoIndicatorData.value[id])
    if (isNaN(res)) {
        return gsInfoIndicatorData.value[id]
    } else {
        return Number(gsInfoIndicatorData.value[id])
    }
}

const isShowJY = () => {
    return (
        getTargetVal('1329047950853120') ||
        getTargetVal('1336355019817984') ||
        getTargetVal('1330464459588608') ||
        getTargetVal('1336598109094912') ||
        (getTargetVal('1334461220258816') && getTargetVal('1334463501960192')) ||
        getTargetVal('1336600185275392')
    )
}

const toCompany = () => {
    if (!props.socialCreditCode) return
    router.push({
        name: 'company-detail',
        query: { socialCreditCode: props.socialCreditCode },
    })
}

onMounted(() => {
    getBaseGsIndicatorData()
})
</script>
<template>
    <div class="font-14 flex flex-column b-margin-4">
        <div>
            快速了解<span class="color-blue" @click="toCompany">{{ name }}</span>
        </div>
        <div>
            <div>
                <!-- 经营 -->
                <div class="t-margin-12 flex flex-row" v-show="isShowJY()">
                    <div class="r-margin-12 small-tag tag-blue">经营</div>
                    <div class="font-14 line-height-24 flex-1">
                        <text class="tag-row-line-hieght">
                            <text v-show="getTargetVal('1329047950853120')">
                                近1年企业营业收入为
                                <text class="color-blue">{{
                                    getTargetVal('1329047950853120')
                                        ? (Number(getTargetVal('1329047950853120')) / 10000).toFixed(2)
                                        : '-'
                                }}</text>
                                万元
                                <text v-show="getTargetVal('1336354550055936')">
                                    <text v-show="getTargetVal('1329047950853120')">，</text>
                                    同比增长
                                    <text class="color-blue">{{ getTargetVal('1336354550055936') ?? '-' }}%</text>
                                </text>
                            </text>
                            <text v-show="getTargetVal('1336355019817984')">
                                <text v-show="getTargetVal('1329047950853120')">，</text>
                                近3年营业收入平均增长率为
                                <text class="color-blue">{{ getTargetVal('1336355019817984') ?? '-' }}%</text>
                            </text>

                            <text v-show="getTargetVal('1330464459588608')">
                                <text v-show="getTargetVal('1336355019817984')">；</text>
                                近1年企业净利润为
                                <text class="color-blue">{{
                                    getTargetVal('1330464459588608')
                                        ? (Number(getTargetVal('1330464459588608')) / 10000).toFixed(2)
                                        : '-'
                                }}</text>
                                万元 ，近一年净利润同比增长
                                <text class="color-blue">{{ getTargetVal('1337761487389696') ?? '-' }}%</text>
                            </text>

                            <text v-show="getTargetVal('1336598109094912')">
                                <text v-show="getTargetVal('1330464459588608')">；</text>
                                近3年净利润年平均增长率为
                                <text class="color-blue">{{ getTargetVal('1336598109094912') ?? '-' }}%</text>
                            </text>

                            <text v-show="getTargetVal('1338100106134528')">
                                <text v-show="getTargetVal('1336598109094912')">，</text>
                                企业整体资产负债率
                                <text class="color-blue">
                                    {{
                                        getTargetVal('1338100106134528')
                                            ? Number(getTargetVal('1338100106134528')).toFixed(2)
                                            : '-'
                                    }}%
                                </text>
                            </text>
                            <text v-show="getTargetVal('1336600185275392')">
                                <text v-show="getTargetVal('1338100106134528')">，</text>
                                近3年总资产年平均增长率为
                                <text class="color-blue">{{ getTargetVal('1336600185275392') ?? '-' }}%</text>
                            </text>
                            <text
                                v-show="
                                    !getTargetVal('1329047950853120') ||
                                    !getTargetVal('1336355019817984') ||
                                    !getTargetVal('1330464459588608') ||
                                    !getTargetVal('1336598109094912') ||
                                    (!getTargetVal('1334461220258816') && !getTargetVal('1334463501960192')) ||
                                    !getTargetVal('1336600185275392')
                                "
                            >
                                。
                            </text>
                        </text>
                    </div>
                </div>

                <!-- 纳税 -->
                <div
                    class="t-margin-12 flex flex-row"
                    v-show="
                        (getTargetVal('1333000788771840') && getTargetVal('1333038453621760')) ||
                        getTargetVal('1336615079248896') ||
                        getTargetVal('1334474193241088') ||
                        getTargetVal('1338017272824832')
                    "
                >
                    <div class="r-margin-12 small-tag tag-blue">纳税</div>
                    <div class="font-14">
                        <text class="tag-row-line-hieght flex-1">
                            <text v-show="getTargetVal('1333000788771840') && getTargetVal('1333038453621760')">
                                近1年纳税总额
                                <text class="color-blue">
                                    {{
                                        (
                                            (Number(getTargetVal('1333000788771840')) +
                                                Number(getTargetVal('1333038453621760'))) /
                                            10000
                                        ).toFixed(2)
                                    }}
                                </text>
                                万元 ，企业所得税纳税总额
                                <text class="color-blue">{{
                                    getTargetVal('1333000788771840')
                                        ? (Number(getTargetVal('1333000788771840')) / 10000).toFixed(2)
                                        : '-'
                                }}</text>
                                万 ，其中增值税纳税总额
                                <text class="color-blue">{{
                                    getTargetVal('1333038453621760')
                                        ? (Number(getTargetVal('1333038453621760')) / 10000).toFixed(2)
                                        : '-'
                                }}</text>
                                万
                            </text>

                            <text v-show="getTargetVal('1336615079248896')">
                                <text v-show="getTargetVal('1333000788771840') && getTargetVal('1333038453621760')"
                                    >，</text
                                >
                                近3年纳税总额平均增长率
                                <text class="color-blue">{{ getTargetVal('1336615079248896') ?? '-' }}%</text>
                            </text>

                            <text v-show="getTargetVal('1334474193241088')">
                                <text v-show="getTargetVal('1336615079248896')">，</text>
                                近1年企业涉税违法事件共
                                <text class="color-blue">{{ getTargetVal('1334474193241088') ?? '-' }}</text>
                                件 ；其中已处理完成
                                <text class="color-blue">{{ getTargetVal('1337000753890304') ?? '-' }}</text>
                                件
                            </text>

                            <text v-show="getTargetVal('1338017272824832')">
                                <text v-show="getTargetVal('1334474193241088')">，</text>
                                企业历史涉税违法事件共
                                <text class="color-blue">{{ getTargetVal('1338017272824832') ?? '-' }}</text>
                                件 ，其中已处理完成
                                <text class="color-blue">{{ getTargetVal('1337001001354240') ?? '-' }}</text>
                                件
                            </text>

                            <text
                                v-show="
                                    !getTargetVal('1334464185631744') ||
                                    (!getTargetVal('1333000788771840') && !getTargetVal('1333038453621760')) ||
                                    !getTargetVal('1336615079248896') ||
                                    !getTargetVal('1334474193241088')
                                "
                            >
                                。
                            </text>
                        </text>
                    </div>
                </div>
            </div>
            <!-- 创新 -->
            <div
                class="t-margin-12 flex flex-row"
                v-show="
                    Number(getTargetVal('1340301226542080')) > 0 ||
                    Number(getTargetVal('1340298017899520')) > 0 ||
                    Number(getTargetVal('1336619827201024')) > 0
                "
            >
                <div class="r-margin-12 small-tag tag-blue">创新</div>
                <div class="font-14 tag-row-line-hieght flex-1">
                    <text
                        v-show="
                            Number(getTargetVal('1340301226542080')) > 0 ||
                            Number(getTargetVal('1340298017899520')) > 0 ||
                            Number(getTargetVal('1336619827201024')) > 0
                        "
                    >
                        企业当前累计已申请的知识产权数量共计
                        <text class="color-blue">
                            {{
                                Number(getTargetVal('1340298017899520')) +
                                    Number(getTargetVal('1336619827201024')) +
                                    Number(getTargetVal('1340301226542080')) || '-'
                            }}
                        </text>
                        件
                    </text>
                    <text v-show="Number(getTargetVal('1340301226542080')) > 0">
                        其中已授权一类知识产权
                        <text class="color-blue">{{ getTargetVal('1340301226542080') ?? '-' }}</text>
                        件
                    </text>
                    <text v-show="Number(getTargetVal('1340298017899520')) > 0">
                        <text v-show="getTargetVal('1340301226542080')">，</text>
                        申请中一类知识产权
                        <text class="color-blue">{{ getTargetVal('1340298017899520') ?? '-' }}</text>
                        件
                    </text>
                    <text v-show="Number(getTargetVal('1336619827201024')) > 0">
                        <text v-show="getTargetVal('1340298017899520')">，</text>
                        已申请二类知识产权
                        <text class="color-blue">{{ getTargetVal('1336619827201024') ?? '-' }}</text>
                        件。
                    </text>
                </div>
            </div>

            <!-- 发展 -->
            <div
                class="t-margin-12 flex flex-row"
                v-show="
                    getTargetVal('1334759552713728') ||
                    getTargetVal('1336621517505536') ||
                    getTargetVal('1327561023947776') ||
                    getTargetVal('1327565763511296')
                "
            >
                <div class="r-margin-12 small-tag tag-blue">发展</div>
                <div class="font-14 tag-row-line-hieght flex-1">
                    <text v-show="getTargetVal('1334759552713728')">
                        企业所属行业为
                        <text class="color-blue">{{ getTargetVal('1334759552713728') ?? '-' }}</text>
                    </text>
                    <text v-show="getTargetVal('1336621517505536')">
                        <text v-show="getTargetVal('1334759552713728')">，</text>
                        截至目前已累计经营
                        <text class="color-blue">{{ getTargetVal('1336621517505536') ?? '-' }}</text>
                        年
                    </text>
                    <text v-show="getTargetVal('1327561023947776')">
                        <text v-show="getTargetVal('1336621517505536')">，</text>
                        已获
                        <text class="color-blue">{{ getTargetVal('1327561023947776') ?? '-' }}</text>
                        轮融资
                    </text>
                    <text v-show="getTargetVal('1327565763511296')">
                        <text v-show="getTargetVal('1327561023947776')">，</text>
                        共计获得融资金额
                        <text class="color-blue">{{ getTargetVal('1327565763511296') ?? '-' }}</text>
                        万
                    </text>
                    <text
                        v-show="
                            !getTargetVal('1334759552713728') ||
                            !getTargetVal('1336621517505536') ||
                            !getTargetVal('1327561023947776') ||
                            !getTargetVal('1327565763511296')
                        "
                    >
                        。
                    </text>
                </div>
            </div>

            <div class="t-margin-12 flex flex-row">
                <div class="r-margin-12 small-tag tag-blue">合规</div>
                <div
                    class="font-14 tag-row-line-hieght flex-1"
                    v-if="
                        getTargetVal('1334466987426816') ||
                        getTargetVal('1333695428428800') ||
                        getTargetVal('1333661907551232') ||
                        getTargetVal('1333674536600576') ||
                        getTargetVal('1333779717161984') ||
                        getTargetVal('1334496469189632') ||
                        getTargetVal('1334504027325440')
                    "
                >
                    <text v-show="getTargetVal('1334466987426816')">
                        企业失信被执行信息共
                        <text class="color-blue">{{ getTargetVal('1334466987426816') }}</text>
                        条 ，其中失信未履行信息
                        <text class="color-blue">{{ getTargetVal('1338023505560576') }}</text>
                        条
                    </text>
                    <text v-show="getTargetVal('1333695428428800')">
                        <text v-show="getTargetVal('1334466987426816')">，</text>
                        近2年失信被执行人信息
                        <text class="color-blue">{{ getTargetVal('1333695428428800') ?? '-' }}</text>
                        条 ，其中失信未履行信息
                        <text class="color-blue">{{ getTargetVal('1338024352809984') ?? '-' }}</text>
                        条
                    </text>
                    <text v-show="getTargetVal('1333661907551232')">
                        <text v-show="getTargetVal('1333695428428800')">；</text>
                        被执行信息共
                        <text class="color-blue">{{ getTargetVal('1333661907551232') ?? '-' }}</text>
                        条 ，其中未结案（含终本结案）信息
                        <text class="color-blue">{{ getTargetVal('1334479343846400') ?? '-' }}</text>
                        条
                    </text>
                    <text v-show="getTargetVal('1333674536600576')">
                        <text v-show="getTargetVal('1333661907551232')">；</text>
                        近2年被执行信息
                        <text class="color-blue">{{ getTargetVal('1333674536600576') ?? '-' }}</text>
                        条 ，其中未结案（含终本结案）信息
                        <text class="color-blue">{{ getTargetVal('1334489422758912') ?? '-' }}</text>
                        条
                    </text>
                    <text v-show="getTargetVal('1333779717161984')">
                        <text v-show="getTargetVal('1333674536600576') || getTargetVal('1333661907551232')">；</text>
                        行政处罚信息
                        <text class="color-blue">{{ getTargetVal('1333779717161984') ?? '-' }}</text>
                        条 ，近2年行政处罚信息
                        <text class="color-blue">{{ getTargetVal('1334494623695872') ?? '-' }}</text>
                        条
                    </text>
                    <text v-show="getTargetVal('1334496469189632')">
                        <text v-show="getTargetVal('1333779717161984')">；</text>
                        经营异常信息
                        <text class="color-blue">{{ getTargetVal('1334496469189632') ?? '-' }}</text>
                        条 ，近2年经营异常信息
                        <text class="color-blue">{{ getTargetVal('1334497022837760') ?? '-' }}</text>
                        条
                    </text>
                    <text v-show="getTargetVal('1334504027325440')">
                        <text v-show="getTargetVal('1334496469189632')">；</text>
                        严重违法信息
                        <text class="color-blue">{{ getTargetVal('1334504027325440') ?? '-' }}</text>
                        条，近2年严重违法信息
                        <text class="color-blue">{{ getTargetVal('1336618849928192') ?? '-' }}</text>
                        条
                    </text>
                    <text
                        v-show="
                            !getTargetVal('1334466987426816') ||
                            !getTargetVal('1333695428428800') ||
                            !getTargetVal('1333661907551232') ||
                            !getTargetVal('1333674536600576') ||
                            !getTargetVal('1333779717161984') ||
                            !getTargetVal('1334496469189632') ||
                            !getTargetVal('1334504027325440')
                        "
                    >
                        。
                    </text>
                </div>
                <div class="color-blue font-14" v-else>该企业信用很好，没有任何合规风险。</div>
            </div>
        </div>
    </div>
</template>
<style scoped lang="scss"></style>
