<template>
    <div class="back-color-common height-100 relative"></div>
    <div class="height-100 back-transparent absolute top-0 overflow-y-auto">
        <div class="content back-transparent">
            <!-- 搜索区域 -->
            <van-search v-model="searchValue" placeholder="请输入您检测的企业名称" background="#00000000" />
            <!-- 列表区域 -->
            <div v-if="totalList > 0" class="font-16 b-margin-8">
                共
                <span class="!color-blue">&nbsp;{{ totalList }}&nbsp;</span>条
            </div>
            <van-list v-model:loading="listLoading" :finished="listFinished" finished-text="没有更多了" @load="onLoad">
                <div
                    class="list-card color-two-grey"
                    v-for="(item, index) in list"
                    :key="index"
                    @click="jump2Detail(item)"
                >
                    <div class="display-flex space-between">
                        <div>
                            <div class="t-padding-16 color-black font-16 maxw-200 text-ellipsis text-nowrap">
                                {{ item.companyName || '-' }}
                            </div>
                            <div class="t-padding-6 color-two-grey font-12">{{ item.socialCreditCode || '-' }}</div>
                        </div>
                        <div class="text-center r-padding-12">
                            <div
                                v-if="item.invoiceCollectDate && item.taxCollectDate && item.healthScore"
                                :class="item.healthScore > 40 ? 'high-level' : 'low-level'"
                            >
                                <span class="font-32 font-bold">{{ item.healthScore }}</span
                                ><span class="font-10 font-normal">分</span>
                            </div>
                            <div
                                v-if="(!item.invoiceCollectDate || !item.taxCollectDate) && item.rateLevel"
                                :class="{
                                    'high-level':
                                        item.rateLevel === 'S' || item.rateLevel === 'A' || item.rateLevel === 'B',
                                    'low-level':
                                        item.rateLevel === 'C' || item.rateLevel === 'D' || item.rateLevel === 'E',
                                }"
                            >
                                <!-- 发票采集或者税务采集 -->
                                <span class="font-32 font-bold">{{ item.rateLevel }}</span
                                ><span class="font-10 font-normal">级</span>
                            </div>
                        </div>
                    </div>

                    <div class="t-padding-20 color-two-grey font-12 display-flex r-padding-16">
                        <div class="r-margin-24">法人：{{ item?.customFields?.legalperson || '-' }}</div>
                        <div>成立日期：{{ item?.customFields?.esdate || '-' }}</div>
                    </div>
                    <div class="color-two-grey font-12">
                        注册资本：{{ item?.customFields?.registercapital || '-' }}万
                    </div>
                    <div class="color-two-grey font-12 r-padding-16 maxw-330 text-ellipsis text-nowrap">
                        地址：{{ item?.customFields?.contactaddress || '-' }}
                    </div>
                </div>
            </van-list>
        </div>
    </div>
    <!-- <div v-else class="absolute height-100 width-100 font-16 display-flex center top-0 left-0">
        <van-button type="primary" class="h-46 width-80" style="background: linear-gradient(to right, #3c74eb, #95d5f4); border: none; border-radius: 8px;" @click="handleTo()">去登录</van-button>
    </div> -->
</template>
<script lang="ts" setup>
import { ref, watch, onMounted, computed } from 'vue'
import { showNotify, showDialog } from 'vant'
import { useRouter } from 'vue-router'
import { tabbarheight } from '@/utils/tabbar-height'
import crmService from '@/service/crmService'
import type { IGetCrmLeadParams, ILeadData } from '@/types/lead'

interface queryDataType {
    socialCreditCode: string
    companyName: string
    preset?: number
}

const paddingBottom = computed(() => {
    return tabbarheight() + 'px'
})
const router = useRouter()
// const token = localStorage.getItem('access_token')
const searchValue = ref()
const list = ref<ILeadData[]>([])
const listLoading = ref(false)
const listFinished = ref(false)
const queryParams = ref<IGetCrmLeadParams>({
    page: 0,
    pageSize: 10,
    isReport: true,
})
const totalList = ref(0)
let timeoutId: number | null = null
watch(
    () => searchValue.value,
    (newVal) => {
        if (timeoutId) {
            clearTimeout(timeoutId)
        }
        timeoutId = window.setTimeout(() => {
            console.log('开始搜索名字', newVal)
            queryParams.value.page = 0
            if (newVal) {
                queryParams.value.page = 0
                queryParams.value.companyName = newVal
            } else {
                queryParams.value = {
                    page: 0,
                    pageSize: 10,
                }
            }
            list.value = []
            listFinished.value = false
            onLoad()
        }, 500)
    }
)

let onLoadTimer: number | null = null
const onLoad = async () => {
    if (onLoadTimer) {
        clearTimeout(onLoadTimer)
    }
    onLoadTimer = window.setTimeout(async () => {
        queryParams.value.page += 1
        let res = await crmService.getCrmList(queryParams.value)
        if (res.errCode === 0) {
            listLoading.value = false
            const { data, total } = res
            totalList.value = total
            if (data && data.length > 0) {
                list.value = list.value.concat(data)
            }

            if (list.value.length >= total) {
                listFinished.value = true
            }
        }
    }, 500)
}
// const handleTo = () => {
//     router.push({
//         name: 'phoneLogin',
//     })
// }
const jump2Detail = (item: ILeadData) => {
    if (item.isDemonstrateData === '1') {
        router.push({
            name: 'companyDetail',
            query: {
                socialCreditCode: item.socialCreditCode,
                companyName: item.companyName,
                preset: 1,
            },
        })
    } else {
        let data = {
            socialCreditCode: item.socialCreditCode,
            companyName: item.companyName,
        }
        crmService.gsGetCompanyClueInfo({ socialCreditCode: item.socialCreditCode }).then((getCrmUserRes) => {
            console.log('getCrmUserRes', getCrmUserRes)
            if (getCrmUserRes.errCode) {
                showDialog({ title: '错误', message: '数据错误，请稍后再试' })
                return
            }

            let res = getCrmUserRes.data
            if (res.clueType) {
                //已经转线索的，刷新
                crmService
                    .refresh({
                        socialCreditCode: item.socialCreditCode,
                        companyName: item.companyName,
                        leadId: res.leadId,
                    })
                    .then(() => {
                        const queryData = ref<queryDataType>({
                            socialCreditCode: item.socialCreditCode,
                            companyName: item.companyName,
                        })
                        if (item?.isDemonstrateData) {
                            queryData.value.preset = 1
                        }
                        router.push({
                            name: 'companyDetail',
                            query: queryData.value,
                        })
                    })
            } else {
                //未转的，转线索
                crmService
                    .crmAdd({
                        clueType: 2,
                        source: 13,
                        ...data,
                    })
                    .then((res) => {
                        if (res.errCode) {
                            showNotify({ type: 'danger', message: res.errMsg })
                            return
                        }
                        router.push({
                            name: 'companyDetail',
                            query: {
                                socialCreditCode: item.socialCreditCode,
                            },
                        })
                    })
            }
        })
    }
}

onMounted(() => {
    console.log('onmounted')
})
</script>

<style lang="scss" scoped>
.content {
    width: 100vw;
    min-height: 100vh;
    padding: 0 16px;
    padding-bottom: v-bind(paddingBottom);
    box-sizing: border-box;

    :deep(.van-search) {
        padding: 12px 0;
    }

    :deep(.van-search__content) {
        background-color: #fff;
    }
}

.list-card {
    padding: 0;
    height: 3.75rem;
    border-radius: 8px;
    background-color: transparent;

    background-image: url('@/assets/images/attention-list-item-bakcground.png');
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    margin-bottom: 12px;

    padding-left: 12px;
}

.high-level {
    background: linear-gradient(to right, #3c74eb, #95d5f4);
    -webkit-background-clip: text; // 将背景裁剪为文字形状
    background-clip: text; // 将背景裁剪为文字形状
    color: transparent;
}

.low-level {
    background: linear-gradient(to right, #ff8d1a, #ff3b52);
    -webkit-background-clip: text; // 将背景裁剪为文字形状
    background-clip: text; // 将背景裁剪为文字形状
    color: transparent;
}
</style>
