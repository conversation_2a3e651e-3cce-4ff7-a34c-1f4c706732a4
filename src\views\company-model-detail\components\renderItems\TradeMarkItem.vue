<template>
    <div class="wrapper flex flex-column row-gap-10 b-margin-10">
        <div class="flex top-bottom-center gap-10">
            <img
                :src="row.MARKIMGOSS"
                class="h-24 w-24"
                style="object-fit: contain; border: 1px solid #f5f5f5; border-radius: 4px"
                alt=""
            />
            <div class="flex-1 font-16">{{ row.MARKNAME }}</div>
        </div>
        <Items
            :columns="others"
            class="flex font-16 lh-18 gap-10"
            :page_config="page_config"
            :row="row"
            :channelType="channelType"
            :modelName="modelName"
        />
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { RequestKeys, type PageConfigItem } from '../../config'
import Items from './Items.vue'

const { page_config, row, channelType, modelName } = defineProps<{
    page_config: PageConfigItem
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    row: any
    channelType: number
    modelName: RequestKeys
}>()

const others = computed(() => {
    const { columns = [] } = page_config
    const otherColumns = columns.filter((item) => !['MARKIMGOSS', 'MARKNAME'].includes(item.key))
    return otherColumns
})
</script>
