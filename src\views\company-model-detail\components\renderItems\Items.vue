<template>
    <div class="flex font-16 lh-18 gap-10" v-for="item in columns" :key="item.key">
        <div class="color-two-grey">{{ item.name }}:</div>
        <div class="flex-1 flex height-100 flex-column color-black">
            <div
                v-if="page_config.key === RequestKeys.Investment && item.key === 'ENTNAME' && row.pid"
                class="pointer"
                style="color: #509de5"
                @click="$emit('toCompanyDetail', row)"
            >
                {{ row.INV || row.ENTNAME }}
            </div>
            <div
                v-else-if="
                    [ColumnType.RelateCompanyLink, ColumnType.CompanyProfileLink].includes(item.type) &&
                    getValue(row, item.key)
                "
                class="flex gap-5 top-bottom-center"
            >
                <RelateLink
                    :data="row"
                    :channel-type="channelType"
                    :name="getValue(row, item.key)"
                    :count="row['personRelatedEntNum'] && Number(row['personRelatedEntNum'])"
                    :model-name="modelName"
                />
            </div>

            <IText
                v-else
                :content="item.render ? item.render(row) : getValue(row, item.key)"
                :max-lines="item.textCollapse ? 3 : 999999"
                :defaultExpanded="item.defaultExpanded ?? (item.textCollapse || false)"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import { ColumnType, RequestKeys, type Column, type PageConfigItem } from '../../config'
import { getNestedValue } from '../../utils'
import IText from './../IText.vue'
import RelateLink from './../RelateLink.vue'

const { columns, page_config, row, channelType, modelName } = defineProps<{
    columns: Column[]
    page_config: PageConfigItem
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    row: any
    channelType: number
    modelName: RequestKeys
}>()
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const getValue = (row: any, key: string) => {
    return getNestedValue(row, key)
}
</script>
