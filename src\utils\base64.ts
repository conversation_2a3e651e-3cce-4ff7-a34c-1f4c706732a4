/**
 * 将字符串(支持中文)编码为 Base64
 */
export function encodeBase64(str: string): string {
    // 先把字符串转成 UTF-8 字节
    const utf8Bytes = new TextEncoder().encode(str)
    // 再转成 base64
    let binary = ''
    utf8Bytes.forEach((b) => (binary += String.fromCharCode(b)))
    return btoa(binary)
}

/**
 * 从 Base64 解码回字符串(支持中文)
 */
export function decodeBase64(base64: string): string {
    const binary = atob(base64)
    const bytes = Uint8Array.from(binary, (c) => c.charCodeAt(0))
    return new TextDecoder().decode(bytes)
}
