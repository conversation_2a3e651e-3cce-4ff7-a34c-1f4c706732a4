<template>
    <div
        v-for="item in groupedColumns"
        :key="item[0].key"
        :class="`${item.length > 1 ? 'change-wrapper' : ''} flex gap-20`"
    >
        <div class="flex-1">
            <div class="label font-14 color-two-grey b-margin-8">{{ item[0].name }}</div>
            <div
                v-if="
                    [ColumnType.RelateCompanyLink, ColumnType.CompanyProfileLink].includes(item[0].type) &&
                    getValue(item[0].key)
                "
                class="flex gap-5 top-bottom-center"
            >
                <RelateLink
                    :data="row"
                    :channel-type="channelType"
                    :name="getValue(item[0].key)"
                    :count="row['personRelatedEntNum'] && Number(row['personRelatedEntNum'])"
                    :model-name="modelName"
                />
            </div>
            <IText
                v-else
                :content="item[0].render ? item[0].render(row) : getValue(item[0].key)"
                :max-lines="item[0].textCollapse ? 3 : 999999"
                :defaultExpanded="item[0].textCollapse || false"
            />
        </div>
        <div v-if="item[1]" class="flex-1">
            <div class="label font-14 color-two-grey b-margin-8">{{ item[1].name }}</div>
            <div
                v-if="page_config.key === RequestKeys.Investment && item[1].key === 'ENTNAME' && row.pid"
                class="pointer"
                style="color: #509de5"
                @click="$emit('toCompanyDetail', row)"
            >
                {{ row.INV || row.ENTNAME }}
            </div>
            <div
                v-else-if="
                    [ColumnType.RelateCompanyLink, ColumnType.CompanyProfileLink].includes(item[1].type) &&
                    getValue(item[1].key)
                "
                class="flex gap-5 top-bottom-center"
            >
                <RelateLink
                    :data="row"
                    :channel-type="channelType"
                    :name="getValue(item[1].key)"
                    :count="row['personRelatedEntNum'] && Number(row['personRelatedEntNum'])"
                    :model-name="modelName"
                />
            </div>
            <IText
                v-else
                :content="item[1].render ? item[1].render(row) : getValue(item[1].key)"
                :max-lines="item[1].textCollapse ? 3 : 999999"
                :defaultExpanded="item[1].textCollapse || false"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { type Column, type PageConfigItem, RequestKeys, ColumnType } from '../../config'
import { getNestedValue } from '../../utils'
import IText from '../IText.vue'
import RelateLink from '../RelateLink.vue'

const {
    columns,
    row,
    page_config,
    modelName,
    channelType,
    col = 2,
} = defineProps<{
    columns: Column[]
    page_config: PageConfigItem
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    row: any
    channelType: number
    modelName: RequestKeys
    col?: number
}>()
const groupedColumns = computed(() => {
    const groups = []
    for (let i = 0; i < columns.length; i += col) {
        groups.push(columns.slice(i, i + col))
    }
    return groups
})
const getValue = (key: string) => {
    return getNestedValue(row, key)
}
</script>

<style lang="scss" scoped>
.change-wrapper {
    position: relative;
    box-sizing: border-box;
    transition: max-height 0.3s ease;
    position: relative;
    &::before {
        content: ' ';
        position: absolute;
        height: 100%;
        width: 1px;
        left: 50%;
        top: 0;
        background-color: #e5e5e5;
    }
}
</style>
