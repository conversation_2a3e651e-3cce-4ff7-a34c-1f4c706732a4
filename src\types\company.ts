import type { INormalFilterParams } from './aic'
import type { ICommonResponse, IPaginationResponse } from './axios'

import type { IAllRecord } from '@/types/record'

export interface ISimpleSearchCompanyResponse {
    name: string
}

export interface ISimpleSearchIndustryMarketResponse {
    name: string
}

export interface SearchCompanyGsInfoResponse {
    items: object[]
    total: number
    page: number
    pageSize: number
    isLock: number
    innerModel?: string
    channelType: number
    companyInfo?: string
}

export interface CompanyTags {
    categoryCode: string
    category: string
    tagName: string
    isDisplay?: boolean
    order?: number
}
export interface GetCompanyDataParams extends IAllRecord {
    socialCreditCode: string
    preset?: number
}
export interface ContactItem {
    custId?: string
    contact: string
    content: string
    tagType: number
    positionContent: string
    type: number
    firstSourceName?: string
    numArea?: string
    allLabels?: string[]
    sources?: IContactSourceItem[]
}

export interface RelateCompany {
    address: string
    entName: string
    pid: string
    position: string
    positionStatus: string
    regCapital: string
    shareholdingRatio: string
}

export interface CompanyBaseInfo {
    name: string
    tags?: Array<CompanyTags>
    legalperson?: string
    esdate?: string
    regCapDisplay?: string
    officialWebsite?: string
    contactaddress?: string
    id: string
    companyName: string
}

export interface GetCompanyContactResponse {
    contacts: Array<ContactItem>
    contactNum?: number
    isLock: number
    channelType: number
}

export interface IndusryMarketItem {
    name: string
    id: string
    useNum: number
}

export interface IGetCompanyByCodeParams {
    keyword: string
    matchType?: string
    page?: number
    pageSize?: number
    [key: string]: string | number | string[] | undefined
}

export interface IGetCompanyByCodeParamsResponse extends IPaginationResponse {
    data: IGetCompanyByCodeParams[]
}

export interface IGetModelCategoryResponse {
    name: string
    currentVersion: {
        jsonStr: string
    }
    title: string
    showType: string
    schemaStrings: string
    children: Array<IGetModelCategoryResponse>
    total?: number
    innerModel?: string
}

export interface ISearchGSInfoParams {
    socialCreditCode: string
    modelName: string
    page?: number
    pageSize?: number
}

export interface ISearchEnterpriseResponse extends IPaginationResponse {
    data: ICompanyInfo[]
}

export interface ICompanyInfo {
    socialCreditCode: string
    id: string
    name: string
    companyName: string
    companyname_ws: string
    contact: string
    legalperson: string
    entstatus: string
    officialWebsite: string
    regCapDisplay: string
    address: string
    contactaddress: string
    highlightName: string
    province: string
    district: string
    secdistrict: string
    enableViewContact: boolean
    hascontact: string
    hasmobile: string
    hasfixed: string
    hasSynced: boolean
    hasSyncedRobot: boolean
    b2bProduct: string
    value: string
    history_name_ws: string
    enttype: string
    enttypedetail: string
    industry: string
    esdate: string
    hasqq: string
    lon: string
    lat: string
    regcapcur: string
    regcapunify: string
    registercapital: string
    hasRelatedContact: string
    opscope: string
    hasATaxCredit: string
    secindustryshort: string
    secondIndustry: string
    thirdIndustry: string
    fourthIndustry: string
    hasATaxCreditCN: string
    type: string
    industry_manufacture: string
    industry_exhibition: string
    hasUnfold: boolean
    email: string
    entNameEng: string
    mobilePhone: string
    fixedPhone: string
    fixedPhoneHyphen: string
    companyArea: string
    contactCount: string
    companyTags: ICompanyTag[]
    tenderCount: string
    tenderType: string
    tenderArea: string
    subjectMatter: string
    tenderPublicDate: string
    projectName: string
    annualSale: string
    contactSource: string
    tradeCount: string
    tradeName: string
    tradeStartTime: string
    tradeEndTime: string
    plantArea: string
    topContact: string
    topContactName: string
    topContactPosition: string
    contactAddressWithLabel: string
    regAddress: string
    uncid: string
    regno: string
    oc: string
    esTimeToNow: string
    relatedContactCount: string
    products: string
    businessscope: string
    baike: string
    semkeywords: string
    jobtitle: string
    clients: string
    shangbiao: string
    guanwang: string
    zhuanli: string
    webchat: string
    companyinfo: string
    app: string
    businessmodel: string
    personnel_info: string
    auction: string
    historyName: string
    firmName: string
    b2bInfo: string
    parCount: string
    exhJournalName: string
    journalId: string
    parArea: string
    parClassification: string
    exhibitionId: string
    brandName: string
    brandName_ws: string
    distance: string
    salesModel: string
    associatedOnlineShopCount: string
    trademarkNum: string
    trademarkTypeStr: string
    latestTrademarkId: string
    latestTrademarkName: string
    latestTrademarkStatus: string
    latestTrademarkApplyDate: string
    trademarkCategory: string
    patentSumInfo: string
    emainBrand: string
    model: string
    isBuy: boolean
    clueInfo: {
        clueType: number
        id?: number
        username?: string
    }
}

export interface ICompanyTag {
    categoryCode: string
    category: string
    tagName: string
    isDisplay: boolean
    name: string
    currentVersion: {
        jsonStr: string
    }
    title: string
    showType: string
    schemaStrings: string
    children: Array<IGetModelCategoryResponse>
    total?: number
    innerModel?: string
}

export interface ISearchGSInfoParams extends IAllRecord {
    socialCreditCode: string
    modelName: string
    page?: number
    pageSize?: number
}

export interface ISearchBidAndFactoryParams {
    belongsArea?: {
        province: string[]
        city: string[]
        district: string[]
    }
    matchField?: string[]
    annualSale?: string[]
    area?: object
    contactType_hasMore?: string[]
    esDate_customer?: string[]
    keyword?: string
    matchType?: string
    model?: string
    page?: number
    pageSize?: number
    plantArea?: string[]
    regCapUnify_customer?: string[]
    scope?: string
    sortBy?: number
    staffs?: string[]
    tenderBusinessType?: string[]
    tenderProjectType?: object[]
    tenderPublishTime?: object
    tenderCompanyRole?: string[]
}

export interface ISearchFactoryItem {
    companyName: string
    entstatus: string
    companyTags: CompanyTags[]
    legalperson?: string
    esdate: string
    regCapDisplay: string
    companyArea?: string
    plantArea?: string
    annualSale?: string
    b2bProduct?: string
    socialCreditCode: string
}

export interface ISearchBidAndFactoryResponse extends IPaginationResponse {
    total: number
    data: ISearchFactoryItem[]
}

export interface ISearchBidProjectResponse {
    total: number
    data: ISearchBidProjectItem[]
}

export interface ISearchBidProjectItem {
    id: string
    name?: string
    highlightName?: string
    contact?: string
    companyTags: CompanyTags[]
    hascontact?: string
    hasmobile?: string
    hasfixed?: string
    hasSynced?: boolean
    hasSyncedRobot?: boolean
    hasUnfold?: boolean
    subjectMatter?: string
    title: string
    tenderType: string
    noticeTypeSub?: string
    province?: string
    district?: string
    tenderPublicDate: string
    companyArea: string
    url?: string
    ossUrl?: string
    bidPrice: string
    budgetPrice?: string
    unit?: string
    tenderUnitList: TenderUnitListItem[]
    outbidUnitList: TenderUnitListItem[]
    agencyUnitList?: object[]
    type?: string
    companyName?: string
}

export interface Contact {
    per?: string
    content: string
}

export interface TenderUnitListItem {
    contact: Contact[]
    name: string
    PID: string
    partyType: string
}

export interface ISearchBidCompanyResponse extends IPaginationResponse {
    total: number
    data: ISearchBidCompanyItem[]
}

export interface ISearchBidCompanyItem {
    id: string
    name: string
    highlightName?: string
    contact?: string
    hascontact?: string
    hasmobile?: string
    hasfixed?: string
    hasSynced: boolean
    hasSyncedRobot: boolean
    hasUnfold: boolean
    companyArea: string
    contactCount: string
    companyTags: CompanyTags[]
    tenderCount: number
    tenderType: string
    tenderArea: string
    subjectMatter?: string
    tenderPublicDate: string
    projectName: string
    legalperson: string
    companyname_ws: string
    value: string
    address: string
    contactaddress: string
    province: string
    district: string
    secdistrict: string
    entstatus: string
    enttype: string
    industry: string
    esdate: string
    regcapcur: string
    regcapunify: string
    registercapital: string
    hasRelatedContact: string
    opscope: string
    socialCreditCode: string
    companyName: string
}

export interface conditionItem {
    cn: string
    cr: string
    cv: (string | number)[] | conditionItem[] | string | number | undefined | { [key: string]: number }
}

export interface ISearchAdvancedSearchParams {
    condition: conditionItem
    sortBy: number
    page: number
    pageSize: number
}

export interface ISearchCompanyItem {
    contact?: string
    name: string
    legalperson: string
    esdate: string
    entstatus: string
    regCapDisplay: string
    officialWebsite: string
    contactaddress: string
    model: string
    socialCreditCode: string
    companyName: string
    isBuy: boolean
    toCenterDistance?: number
    regcapunify?: number
    address?: string
    companyname_ws?: string
}

export interface ISearchAdvancedSearchResponse extends IPaginationResponse {
    data: ICompanyInfo[]
    total: number
    page: number
    pageSize: number
    channelType: number
    realTotal: number
    errMsg?: string
}

export interface ISearchGetCategoryParams extends IAllRecord {
    categoryId?: string
}

export interface ISearchGetCategoryItem {
    isNormal?: boolean
    id: string
    children: ISearchGetCategoryItem[]
    disabled?: boolean
    name: string
    parentId?: string
    shortName?: string
}
export interface ISearchGetCategoryResponse extends IPaginationResponse {
    data: ISearchGetCategoryItem[]
}

export interface ISearchGetTemplateItem {
    id: string
    name: string
    useNum: number
    searchData: { list: IAllRecord[] }
    isModel?: boolean
    categoryIds: string[]
    categoryNames: string[]
    sort: number
}

export interface ISearchGetTemplateResponse extends IPaginationResponse{
    data: ISearchGetTemplateItem[]
    total: number
    page: number
    pageSize: number
    errCode: number
}

export interface ISearchGetTemplateParams extends IAllRecord {
    templateId?: string
    page: number
    pageSize: number
    searchType?: string
}

export type FilterResultValue =
    | string
    | string[]
    | {
          [mappedKey: string]: string[]
      }[]
    | {
          province: string[]
          city: string[]
          district: string[]
      }

export type IPushToGlobal = INormalFilterParams | INormalFilterParams[] | Record<string, string>

export interface SearchKeyItem {
    key: string
    title: string
}

export interface TagTypeCk {
    searchKey?: string
    searchKeys?: SearchKeyItem[]
    searchTitle?: string
}

export interface PropsRow {
    [key: string]: string
}

export interface IGetBasicInfoParams extends IAllRecord {
    socialCreditCode: string
    preset?: number
}

export interface IShareholderItem {
    personRelatedEntNum: string
    INSTO: string
    INV: string
    LIACCONAM: string
    LISUBCONAM: string
    pid: string
    INVEST_TYPE: string
    INVTYPE: string
}

export interface IKeyPersonItem {
    name: string
    personRelatedEntNum: string
    position_CN: string
}

export interface IContactSourceItem {
    createdDate: number
    address: string
    contact: string
    domain: string
    positionDesc: string
    sourceName: string
    updatedDate: string
    job: string
    url: string
}

export interface IGetSectionNumParams extends IAllRecord {
    socialCreditCode: string
}

export interface IGetSectionNumResponse extends ICommonResponse {
    data: ISectionItem[]
}

export interface ISectionNumItem {
    isHighLight: boolean
    num: number
    icon: string
    label: string
    labelName: string
}

export interface ISectionItem {
    values: ISectionNumItem[]
    section: string
    sectionName: string
    isLock: number // 1锁 0 不锁
}

export interface IGetTaxStatusParams extends IAllRecord {
    socialCreditCode: string
}

export interface IGetTaxStatusResponse extends ICommonResponse {
    data: ITaxStatus
}

export interface ITaxStatus {
    qdStatus: number
    version: {
        id: number
        merchantId: number
        type: number
        authStartDate: string
        authEndDate: string
        operatorId: string
        authType: number
        version: string
        isSpecial: number
        createTime: string
        updateTime: string
        versionStr: string
        versionFlag: true
    }
    status: number
    statusStr: string
}

export interface ISearchGetUserInfoParams extends IAllRecord {
    dataPlatform: string
    requestPlatform: string
}

export interface ISearchGetUserInfoResponse extends ICommonResponse {
    data: ISearchGetUserInfo
}

export interface ISearchGetUserInfo {
    platformUserId: string
    encryptionData: string
    zqy: string
    zpy: string
}
