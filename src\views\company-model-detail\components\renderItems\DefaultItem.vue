<template>
    <div class="wrapper flex flex-column row-gap-10 b-margin-10">
        <Items
            :columns="page_config.columns || []"
            class="flex font-16 lh-18 gap-10"
            :page_config="page_config"
            :row="row"
            :channelType="channelType"
            :modelName="modelName"
        />
    </div>
</template>

<script setup lang="ts">
import { RequestKeys, type PageConfigItem } from '../../config'
import Items from './Items.vue'

const { page_config, row, channelType, modelName } = defineProps<{
    page_config: PageConfigItem
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    row: any
    channelType: number
    modelName: RequestKeys
}>()
</script>
