<script lang="ts" setup>
import { ref } from 'vue'
import type { IGetCrmLeadParams, IRiskListItem } from '@/types/lead'
import crmService from '@/service/crmService'
import { useRouter } from 'vue-router'
import noDataPng from '@/assets/hub-images/no-data.png'
const router = useRouter()
const listLoading = ref(false)
const listFinished = ref(false)
let onLoadTimer: number | null = null
const queryParams = ref<IGetCrmLeadParams>({
    page: 0,
    pageSize: 10,
})
const listData = ref<IRiskListItem[]>([])
const totalNum = ref(0)
const isShowEmpty = ref(false)
const onLoad = async () => {
    if (onLoadTimer) {
        clearTimeout(onLoadTimer)
    }
    onLoadTimer = window.setTimeout(async () => {
        queryParams.value.page += 1
        let res = await crmService.crmRiskRiskList(queryParams.value)
        console.log('res', res, res.errCode)

        if (res.errCode === 0) {
            listLoading.value = false
            const { data, total } = res
            if (total === 0) {
                isShowEmpty.value = true
            } else {
                isShowEmpty.value = false
            }
            totalNum.value = total
            if (data && data.length > 0) {
                listData.value = listData.value.concat(data)
            }

            if (listData.value.length >= total) {
                listFinished.value = true
            }
        }
    }, 500)
}
const riskLevel2Info = (label?: number) => {
    // ["primary", "success", "info", "warning", "danger"]
    if (label === 100) {
        return {
            type: 'danger',
            label: '高风险',
        }
    } else if (label === 50) {
        return {
            type: 'warning',
            label: '中风险',
        }
    } else if (label === 1) {
        return {
            type: 'green',
            label: '低风险',
        }
    } else if (label === 0) {
        return {
            type: 'primary',
            label: '无风险',
        }
    } else {
        return {
            type: 'primary',
            label: '无风险',
        }
    }
}
const operationStatusLabel2Class = (val: string) => {
    if (val.includes('存续')) {
        return 'green'
    } else if (
        val.includes('迁入') ||
        val.includes('迁出') ||
        val.includes('注销') ||
        val.includes('吊销') ||
        val.includes('撤销')
    ) {
        return 'danger'
    } else {
        return 'primary'
    }
}
const openCompanyDetail = (soc: string) => {
    if (!soc) return
    router.push({
        name: 'company-detail',
        query: { socialCreditCode: soc },
    })
}
</script>
<template>
    <div class="width-100 height-100 relative" style="background-color: #f3f5f8">
        <div class="width-100 height-100 display-flex flex-column absolute top-0 overflow-y-auto border-box">
            <!-- 顶部搜索栏 -->
            <!-- <div
            class="display-flex space-between top-bottom-center font-14 all-padding-16"
            style="background-color: #fff"
        >
            <div class="flex-1 color-black display-flex top-bottom-center">
                <div @click="poolVisible = !poolVisible">{{ selectedPoolInfo?.name }}</div>
                <Icon icon="icon-a-Frame1171276219" :size="16" color="var(--main-black)" />
                <van-popup v-model:show="poolVisible" position="top" round close-on-click-overlay>
                    <div
                        class="h-40 display-flex space-between top-bottom-center tb-padding-8 lr-padding-16 color-black"
                        :class="item.id === selectedPoolInfo?.id ? 'active' : ''"
                        style="border-bottom: 1px solid var(--border-color)"
                        v-for="item in poolList"
                        :key="item.id"
                        @click="handleChangeActiveTab(item)"
                    >
                        <div class="font-16">{{ item.name }}</div>
                        <Icon
                            v-if="item.id === selectedPoolInfo?.id"
                            icon="icon-a-Frame1171275825"
                            :size="28"
                            color="var(--main-blue-)"
                        ></Icon>
                    </div>
                </van-popup>
            </div>
            <searchFilter
                :searchOptionKey="'CUSTOMER_SEARCH_OPTIONS'"
                :customConfig="customSearchOptionConifg"
                :tabType="selectedPoolInfo.id"
                @updateSearchParams="updateSearchParams"
            ></searchFilter>
        </div> -->
            <!-- 表格区域 -->
            <div class="flex-1 lr-padding-10">
                <div class="display-flex space-between top-bottom-center tb-padding-12">
                    <div class="font-13 lr-padding-11" style="color: #8591a6">共{{ totalNum }}条</div>
                </div>
                <van-empty v-if="isShowEmpty" :image="noDataPng" image-size="10rem" description="暂无数据" />
                <van-list
                    v-else
                    v-model:loading="listLoading"
                    :finished="listFinished"
                    finished-text="没有更多了"
                    @load="onLoad"
                >
                    <div
                        class="border-radius-8 b-margin-8 all-padding-16 color-two-grey"
                        style="background-color: #fff"
                        v-for="(item, index) in listData"
                        :key="index"
                        @click="openCompanyDetail(item.socialCreditCode)"
                    >
                        <div class="width-100 display-flex space-between b-margin-12">
                            <img
                                class="w-45 h-45 r-margin-8"
                                src="@/assets/hub-images/company-icon.png"
                                alt="暂无图片"
                            />
                            <div class="flex-1">
                                <div class="maxw-280 color-black font-14 text-ellipsis text-nowrap b-margin-2">
                                    {{ item.companyName || '-' }}
                                </div>
                                <div class="display-flex">
                                    <!-- 登记状态 -->
                                    <div
                                        v-if="item.operationStatusLabel"
                                        class="tb-padding-2 lr-padding-9 lh-16 border-radius-2 font-12 r-margin-4"
                                        :class="operationStatusLabel2Class(item.operationStatusLabel)"
                                    >
                                        {{ item.operationStatusLabel }}
                                    </div>
                                    <!-- 风险等级 -->
                                    <div
                                        class="tb-padding-2 lr-padding-9 lh-16 border-radius-2 font-12"
                                        :class="riskLevel2Info(item.riskLevel).type"
                                    >
                                        {{ riskLevel2Info(item.riskLevel).label }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if="item.riskTypeOverView.length > 0" class="display-flex flex-wrap gap-4">
                            <div class="w-70 font-14 color-two-grey">风险概述：</div>
                            <div
                                class="tb-padding-2 lr-padding-9 lh-16 border-radius-2 font-12 color-red"
                                style="border: 1px solid var(--main-red-)"
                                v-for="(op, index) in item.riskTypeOverView"
                                :key="index"
                            >
                                {{ op.label }}x{{ op.num }}
                            </div>
                        </div>
                        <div v-else class="display-flex">
                            <div class="w-70 font-14 color-two-grey">风险概述：</div>
                            <div
                                class="tb-padding-2 lr-padding-9 lh-16 border-radius-2 font-12"
                                style="color: #999999; background-color: #f2f5f8"
                            >
                                暂无风险概述
                            </div>
                        </div>
                    </div>
                </van-list>
            </div>
        </div>
    </div>
</template>
<style scoped lang="scss">
.active {
    color: var(--main-blue-);
}
.primary {
    color: var(--main-blue-);
    background-color: #e6f0ff;
}
.green {
    color: var(--main-green-);
    background-color: #e5f8f0;
}
.warning {
    color: var(--main-orange);
    background-color: #fff1e0;
}
.danger {
    color: #fff;
    background-color: #ed5d28;
}
</style>
