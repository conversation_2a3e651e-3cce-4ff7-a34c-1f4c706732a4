import type { IWechat } from './types/wechat'
import type { parseTime } from './utils/parse-time'

declare global {
    interface Window {
        initTAC: (path: string, config: IConfig) => Promise<ITacProps>
        parseTime: typeof parseTime
        ShuzuChatSDK: function
        document: {
            title: string
        }
        VConsole: function
        wx: IWechat
    }
}

declare const WeixinJSBridge: {
    // eslint-disable-next-line
    invoke: (api: string, params: Record<string, any>, callback: (res: any) => void) => void
    // eslint-disable-next-line
    on: (event: string, callback: (res: any) => void) => void
}
