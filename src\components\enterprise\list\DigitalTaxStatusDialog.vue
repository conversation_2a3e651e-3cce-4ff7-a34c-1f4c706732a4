<script lang="ts" setup>
import aicService from '@/service/aicService'
import { encodeBase64 } from '@/utils/base64'
import { showFailToast } from 'vant'
import { computed, onMounted, ref } from 'vue'

const props = defineProps<{
    socialCreditCode: string
    name: string
    close: () => void
}>()

const statusStr = ref('')
const status = ref<number | null>(null)
const loading = ref(false)
const showBtn = ref(false)
const encryptionDataRef = ref('')

// status 0未开通，1已开通，2不可开通
const getData = () => {
    if (!props.socialCreditCode) return
    loading.value = true
    aicService
        .searchCheckActivate({
            socialCreditCode: props.socialCreditCode || '',
        })
        .then((res) => {
            loading.value = false
            const { errCode, data } = res
            if (errCode === 0) {
                statusStr.value = data.statusStr
                status.value = data.status
                if (data.status === 0) {
                    queryEncryptData('开通数电发票业务')
                }
                if (data.status === 1) {
                    queryEncryptData('查看详情')
                }
            }
        })
}

const queryEncryptData = (title: string) => {
    aicService
        .searchGetUserInfo({
            dataPlatform: 'zpy',
            requestPlatform: 'zqy',
        })
        .then((res) => {
            if (res.errCode === 0) {
                const { data } = res
                if (data.encryptionData) {
                    encryptionDataRef.value = data.encryptionData
                    loadWxBtn(data.encryptionData, title)
                }
            } else {
                showFailToast(res.errMsg || '无法三方参数')
            }
        })
}

const loadWxBtn = (encryptionData: string, title: string) => {
    if (!isInWeixin.value) return
    const env = import.meta.env.VITE_APP_ENV === 'production' ? 'release' : 'trial'
    const path = `/pages/merchant-register/index?nsrsbh=${props.socialCreditCode}&nsrmc=${props.name}&encryptData=${encryptionData}`
    const element = document.getElementById(`tax-btn`)
    if (!element) return
    element.innerHTML = `<wx-open-launch-weapp id='launch-btn' appid='wxd7d2a716814f8aba' path=${path} style='width:100%' env-version=${env}>
                <template>
                    <div style='width: 100%; height: 32px; background: #1989fa; display: flex; justify-content: center; align-items: center;font-size: 12px; color: white'>${title}</div>
                </template>
            </wx-open-launch-weapp>`
    showBtn.value = true
}

const toMini = () => {
    const env = import.meta.env.VITE_APP_ENV === 'production' ? 'release' : 'trial'

    // b64n base64编码的企业名称
    const parmas = `nsrsbh=${props.socialCreditCode}&b64n=${encodeBase64(props.name)}&encryptData=${encryptionDataRef.value}`
    const query = encodeURIComponent(parmas)
    const path = `weixin://dl/business/?appid=wxd7d2a716814f8aba&path=pages/merchant-register/index&query=${query}&env_version=${env}`
    location.href = path
}

const isInWeixin = computed(() => {
    return window.navigator.userAgent.toLowerCase().indexOf('micromessenger') !== -1
})

onMounted(() => {
    getData()
})
</script>
<template>
    <div class="all-padding-24 flex flex-column gap-4">
        <div class="font-16 color-black">{{ name || '' }}</div>
        <div class="flex flex-column t-margin-12 gap-6">
            <div class="font-12 color-text-grey">
                <span>统一社会信用代码：{{ socialCreditCode || '' }}</span>
            </div>
            <div class="font-12 color-text-grey">
                <span>数电业务状态：{{ statusStr }}</span>
            </div>
        </div>
        <div class="flex flex-column t-margin-32 gap-6 h-72" v-if="status === 0">
            <div class="flex width-100 center border-radius-4 oh" id="tax-btn" v-if="isInWeixin"></div>
            <div class="flex width-100 center border-radius-4 oh" v-if="!isInWeixin">
                <van-button type="primary" size="small" class="width-100" @click="toMini">开通数电发票业务</van-button>
            </div>
            <div class="flex width-100 center">
                <van-button
                    type="default"
                    size="small"
                    class="width-100"
                    @click="close()"
                    v-if="showBtn || !isInWeixin"
                >
                    暂不开通
                </van-button>
            </div>
        </div>
        <div class="flex flex-column t-margin-32 gap-6" v-if="status === 1">
            <div class="flex width-100 center border-radius-4 oh" id="tax-btn" v-if="isInWeixin"></div>
            <div class="flex width-100 center border-radius-4 oh" v-if="!isInWeixin">
                <van-button type="primary" size="small" class="width-100" @click="toMini">查看详情</van-button>
            </div>
            <div class="flex width-100 center">
                <van-button
                    type="default"
                    size="small"
                    class="width-100"
                    @click="close()"
                    v-if="showBtn || !isInWeixin"
                >
                    返回
                </van-button>
            </div>
        </div>
        <div class="flex flex-column t-margin-32 gap-6" v-if="status === 2">
            <div class="flex width-100 center">
                <van-button type="primary" size="small" class="width-100" @click="close()">知道了</van-button>
            </div>
        </div>
    </div>
</template>
