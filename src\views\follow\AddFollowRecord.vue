<template>
    <div class="home all-padding-12 border-box" style="background-color: #F2F5F8">
        <div class="display-flex all-padding-16 flex-column gap-16 font-14 border-radius-8" style="color:#666666;background-color: #ffffff;">
            <span class='font-16 color-black font-weight-600'>跟进信息</span>
            <div class="t-margin-8 display-flex space-between">
                <span>对应线索</span>
                <div>{{ companyName }}</div>
            </div>
            <div class="border-bottom"></div>
            <div class="display-flex space-between">
                <span>跟进方式</span>
                <div display="flex-center" @click="showFollowType = true">
                    <span class="r-margin-4" :style="{ color: followType === '请选择跟进方式' ? '#999999' : '#222222' }">{{ followType }}</span>
                    <van-icon v-if='followType === "请选择跟进方式"' name="arrow" />
                    <van-icon v-else name="close" @click.stop="resetFollowType" />
                </div>
            </div>
            <div class="border-bottom"></div>
            <div class="display-flex space-between">
                <span>实际跟进时间</span>
                <div display="flex-center" @click="showRealFollowDate = true">
                    <span class="r-margin-4" :style="{ color: realFollowDate === '请选择实际跟进时间' ? '#999999' : '#222222' }">{{ realFollowDate }}</span>
                    <van-icon v-if='realFollowDate === "请选择实际跟进时间"' name="arrow" />
                    <van-icon v-else name="close" @click.stop="resetRealFollowData" />
                </div>
            </div>
            <div class="border-bottom"></div>
            <div class="display-flex space-between">
                <span>下次跟进时间</span>
                <div display="flex-center" @click="showNextFollowDate = true">
                    <span class="r-margin-4" :style="{ color: nextFollowDate === '请选择下次跟进时间' ? '#999999' : '#222222' }">{{ nextFollowDate }}</span>
                    <van-icon v-if='nextFollowDate === "请选择下次跟进时间"' name="arrow" />
                    <van-icon v-else name="close" @click.stop="resetNextFollowData" />
                </div>
            </div>
        </div>
        <div class="t-margin-16 display-flex all-padding-16 flex-column gap-16 font-14 border-radius-8" style="color:#666666;background-color: #ffffff;">
            <div class="display-flex space-between">
                <span class="font-16 font-weight-600">
                    <span class="color-red">*</span>
                    写跟进
                </span>
            </div>
            <van-field
                v-model="description"
                type="textarea"
                maxlength="200"
                placeholder="请输入跟进信息，最多支持输入200字符..."
            >
            </van-field>
            <van-uploader 
                :before-read="beforeRead"
                :after-read="afterRead" 
                result-type="file" 
                v-model="fileList"
                preview-size="2.5rem" 
                multiple 
                :max-size="5 * 1024 * 1024" 
                @oversize="onOversize" 
                :preview-full-image=false 
            >
            </van-uploader>
        </div>
    </div>
    <van-button class="fixed-bottom" type="primary" @click="onSubmit">提 交</van-button>
    <van-popup v-model:show="showFollowType" destroy-on-close round position="bottom">
        <van-picker
            :columns="followTypeColumns"
            @cancel="showFollowType = false"
            @confirm="onFollowTypeConfirm"
        />
    </van-popup>
    <van-popup v-model:show="showRealFollowDate" destroy-on-close round position="bottom">
        <van-date-picker
            v-model="currentDate"
            title="选择日期"
            :min-date="minDate"
            :max-date="maxDate"
            @confirm="onRealFollowDateConfirm()"
        />
    </van-popup>
    <van-popup v-model:show="showNextFollowDate" destroy-on-close round position="bottom">
        <van-date-picker
            v-model="currentDate"
            title="选择日期"
            :min-date="minDate"
            @confirm="onNextFollowDateConfirm()"
        />
    </van-popup>
</template>

<script lang='ts' setup>
import { ref, computed, onMounted, watch } from 'vue'
import { tabbarheight } from '@/utils/tabbar-height'
import { useRoute,useRouter } from 'vue-router'
import crmService from '@/service/crmService'
import type { IUpdateActiviesParams } from '@/types/lead'
import { showToast, type UploaderFileListItem } from 'vant'

const router = useRouter()
const route = useRoute()
const paddingBottom = computed(() => {
    return tabbarheight() + 'px'
})
// 跟进方式相关
const followTypeColumns = [
    { text:'电话',value:'电话' },
    { text:'微信',value:'微信' },
    { text:'短信',value:'短信' },
    { text:'拜访',value:'拜访' },
    { text:'面谈',value:'面谈' },
    { text:'其他',value:'其他' },
]
const followType = ref('请选择跟进方式')
const showFollowType = ref(false)
const followTypeValue = ref<string>('')
interface SelectedOption {
    text: string
    value: string
}
const resetFollowType = () => {
    followType.value = '请选择跟进方式'
    followTypeValue.value = ''
}
const onFollowTypeConfirm = ({ selectedOptions } :{ selectedOptions: SelectedOption[] }) => {
    // console.log(selectedOptions)
    followType.value = selectedOptions[0].text
    followTypeValue.value = selectedOptions[0].value
    showFollowType.value = false
}

// 实际时间相关
const realFollowDate = ref('请选择实际跟进时间')
const realFollowDateValue = ref<number>()
const showRealFollowDate = ref(false)
const nextFollowDate = ref('请选择下次跟进时间')
const nextFollowDateValue = ref<number>()
const showNextFollowDate = ref(false)

const formatDate = (date: Date): string[] => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return [year.toString(), month, day]
}
const currentDate = ref(formatDate(new Date()))
const minDate = new Date(new Date().getFullYear()-5,1,1)
const maxDate = new Date(new Date().getFullYear()+5,12,31)
const onRealFollowDateConfirm = () => {
    // console.log(currentDate.value)
    realFollowDate.value = `${currentDate.value[0]}年${currentDate.value[1]}月${currentDate.value[2]}日`
    realFollowDateValue.value = new Date(Number(currentDate.value[0]),Number(currentDate.value[1])-1,Number(currentDate.value[2])).getTime()
    // console.log(realFollowDateValue.value)
    showRealFollowDate.value = false
}
const onNextFollowDateConfirm = () => {
    nextFollowDate.value = `${currentDate.value[0]}年${currentDate.value[1]}月${currentDate.value[2]}日`
    nextFollowDateValue.value = new Date(Number(currentDate.value[0]),Number(currentDate.value[1])-1,Number(currentDate.value[2])).getTime()
    showNextFollowDate.value = false
}
const resetRealFollowData = () => {
    // console.log('resetRealFollowData')
    realFollowDate.value = '请选择实际跟进时间'
    realFollowDateValue.value = undefined
}
const resetNextFollowData = () => {
    nextFollowDate.value = '请选择下次跟进时间'
    nextFollowDateValue.value = undefined
    console.log(nextFollowDateValue.value)
}
// 写跟进相关
const description = ref('')
// 重置跟进相关数据
const resetOtherData = () => {
    fileList.value = []
    description.value = ''
    params.value = {
        activityType: 'create',
        leadId: route.query.leadId as string
    }
}

// 预览的图片
const fileList = ref<UploaderFileListItem[]>([])

const beforeRead = (file: File ) => {
    if (file.type !== 'image/jpeg' && file.type !== 'image/png' && file.type !== 'image/gif' && file.type !== 'image/jpg') {
        showToast('请上传图片类型的文件!')
        return false
    }
    return true
}
// 上传图片
const afterRead = (file: UploaderFileListItem ) => {
    file.status = 'uploading'
    file.message = '上传中...'
    // 处理单个文件或文件数组
    const fileItem = Array.isArray(file) ? file[0] : file
    // 检查是否有实际的文件对象
    if (!fileItem.file) {
        console.error('没有找到文件对象')
        return
    }
    const formData = new FormData()
    // 使用 file.file 属性，这是实际的 File 对象
    formData.append('file', fileItem.file)

    crmService.crmFileUpload(formData).then((res) => {
        console.log('上传成功:', res)
        file.status = 'done'
        file.message = '上传成功'
        file.url = res.data.link
        // console.log('fileList',fileList.value)
    }).catch((error) => {
        console.error('文件上传失败:', error)
        file.status = 'failed'
        file.message = '上传失败'
        // console.log('fileList',fileList.value)        
    })
}
const onOversize = () => {
    showToast('文件大小不可超过5mb!')
}

const companyName = route.query.companyName
const params = ref<IUpdateActiviesParams>({
    activityType: 'create',
    leadId: route.query.leadId as string
})
watch([followTypeValue, realFollowDateValue, nextFollowDateValue, description, fileList], ([newFollowTypeValue, newRealFollowDateValue, newNextFollowDateValue,description,fileList]) => {
    // console.log('watch triggered',[newFollowTypeValue, newRealFollowDateValue, newNextFollowDateValue])
    const updatedParams = {
        activityType: 'create',
        leadId: route.query.leadId as string
    } as IUpdateActiviesParams
    if (newFollowTypeValue) {
        updatedParams.followType = newFollowTypeValue
        // console.log('followTypeValue:', newFollowTypeValue)
    }
    if (newRealFollowDateValue) {
        updatedParams.realFollowDate = newRealFollowDateValue
        // console.log('realFollowDateValue:', newRealFollowDateValue)
    }
    if (newNextFollowDateValue) {
        updatedParams.nextFollowDate = newNextFollowDateValue
        // console.log('nextFollowDateValue:', newNextFollowDateValue)
    }
    if (description){
        updatedParams.description = description
    }
    if (fileList && fileList.length > 0){
        updatedParams.followImg = fileList.filter((item) => item.status === 'done').map((item) => {
            return{
                name: item.url?.split('com/')[1] as string
            }
        })
    }
    // console.log('123123',updatedParams)
    params.value = updatedParams
    // console.log('params:', params.value)
},{ deep: true })

const followImg = computed(() => {
    return fileList.value.filter((item) => item.status === 'done').map((item) => {
        return {
            name: item.url as string
        }
    })
})

const onSubmit =async () => {
    if(!description.value && !fileList.value.length){
        showToast('请填写跟进信息')
        return
    }
    if(followImg.value.length !== fileList.value.length){
        showToast('图片尚未上传成功，请稍等')
    }
    console.log('followImg',followImg.value)
    console.log('fileList',fileList.value)
    console.log('params.value',params.value)
    const res = await crmService.crmAddActivies(params.value)
    console.log('res',res)
    if(res.success){
        showToast('新增成功')
        resetOtherData()
        resetFollowType()
        resetRealFollowData()
        resetNextFollowData()
        router.back()
    }else{
        showToast(res.errMsg)
    }
}

onMounted(() => {

})
</script>

<style lang='scss' scoped>

.home {
    height: 100%;
    overflow: scroll;
    padding-bottom: v-bind(paddingBottom);
    display: flex;
    flex-direction: column;
}

.fixed-bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    margin: 10px;
    padding: 16px;
    background-color: #1989fa;
    color: white;
    border: none;
    border-radius: 32px;
    z-index: 1000;
}

:deep(.van-cell){
    padding: 0px !important;
}
</style>