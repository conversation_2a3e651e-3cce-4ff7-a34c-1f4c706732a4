<template>
    <div class="all-padding-16 oh border-box" style="background-color: #ffffff">
        <div class="display-flex top-bottom-center font-16 space-between b-margin-8">
            <span class="font-weight-600">销售动态</span>
            <div class="display-flex top-bottom-center gap-4" @click="jumpToAdd">
                <van-icon name="edit" color="#3C74EB" size="20" />
                <span class="color-blue">写跟进</span>
            </div>
        </div>
        <van-list v-model:loading="loading" :finished="finished" finished-text="暂无数据" @load="onLoad">
            <div v-if="dataList && dataList.length > 0">
                <van-cell v-for="(item,index) in dataList.slice(0,2)" :key="index" >
                    <div style="text-align: left;">
                        <div class="display-flex top-bottom-center gap-4">
                            <div style="height: 0.2rem; width: 0.2rem; border-radius: 50%; border: 2px solid #2b83fd"></div>
                            <div class="font-16 color-black font-weight-500">{{ moment(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
                            <div v-if="item.followType" class="lr-padding-8 border-radius-4 l-margin-8" style="background-color: #F2F5F8;border:1px solid #f2f2f2;">
                                <span class="font-14" style="color:#666666">{{ item.followType }}</span> 
                            </div>
                        </div>
                        <div class="tb-margin-4 l-margin-16 color-black" v-if="item.description" >
                            {{ item.description }}
                        </div>
                        <div class="l-margin-16 display-flex top-bottom-center">
                            <div class="font-16">
                                来自{{ item.referType == 'lead' ? '线索' : item.referType == 'customer' ? '客户' : '-' }}:{{ item.referName }}
                            </div>
                        </div>
                        <!-- 图片 -->
                        <div v-if="item.followImg && item.followImg.length > 0" class="flex-wrap display-flex top-bottom-center gap-4 t-margin-12">
                            <div v-for="(img,index) in item.followImg" :key="index">
                                <img
                                    :src="fileService.getFileUrl(img.name)"
                                    alt=""
                                    class="w-50 h-50"
                                    @click="previewImg(img.name)"
                                />
                            </div>
                        </div>
                    </div>
                </van-cell>
            </div>
            <div v-else class="font-16 flex-center tb-margin-12">暂无数据</div>
            <div v-if="dataList && dataList.length > 0" class="flex-center font-16 color-blue" @click="jumpToList">
                查看更多
                <van-icon name="arrow" size="18" color="#3C74EB" />
            </div>
        </van-list>
    </div>
    <van-overlay :show="showImg" @click="showImg = false">
        <div class="wrapper">
            <!-- <div class="block" @click.stop ></div> -->
            <img :src="fileService.getFileUrl(currentImg)" alt="" class="w-300" />
        </div>
    </van-overlay>
</template>

<script lang='ts' setup>
import crmService from '@/service/crmService'
import fileService from '@/utils/fileService'
import type { ICrmGetActiviesParams, ICrmGetActiviesItem } from '@/types/lead'
import { ref, onMounted, getCurrentInstance } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const props = defineProps<{ 
    leadId: string 
    companyName: string 
}>()
const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment

const showImg = ref<boolean>(false)
const currentImg = ref<string>('')
const previewImg = (img:string) => {
    console.log('previewImg',img)
    currentImg.value = img
    showImg.value = true
}

const loading = ref<boolean>(false)
const finished = ref<boolean>(false)
const dataList = ref<ICrmGetActiviesItem[]>([])
const queryParams = ref<ICrmGetActiviesParams>({
    leadId:props.leadId || '1958849538943840258',
    page:1,
    pageSize:10,
})
const search = async (params: ICrmGetActiviesParams) => {
    const res = await crmService.crmGetActivities(params)
    dataList.value.push(...res.data)
    return res
}
let ti: ReturnType<typeof setTimeout> | null = null
const onLoad = () => {
    if (ti) {
        clearTimeout(ti)
    }
    ti = setTimeout(async () => {
        await search(queryParams.value)
        queryParams.value.page += 1
        loading.value = false
        // console.log('benefitList.value.length',dataList.value.length)
        finished.value = true
    },100)
}

const jumpToAdd = () => {
    router.push({
        name: 'add-follow-record',
        query:{
            leadId: props.leadId || '1958849538943840258',
            companyName: props.companyName || '数族（南京）科技股份有限公司'
        }
    })
}

const jumpToList = () => {
    router.push({
        name: 'follow-record', 
        query: { 
            leadId: props.leadId || '1958849538943840258', 
            companyName: props.companyName || '数族（南京）科技股份有限公司'
        } 
    })
}

onMounted(() => {
    console.log('props',props.leadId,props.companyName)
})

</script>

<style lang='scss' scoped>
:deep(.van-list__finished-text){
    display: none !important;
}

.wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.block {
    width: 120px;
    height: 120px;
    background-color: #fff;
}
</style>
