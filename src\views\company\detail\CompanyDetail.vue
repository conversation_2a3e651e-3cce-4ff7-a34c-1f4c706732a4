<script setup lang="ts">
import { useRoute } from 'vue-router'
import { Main } from './components'
import { ref, watch } from 'vue'
const route = useRoute()
const resetKey = ref(0)

defineOptions({
    name: 'company-detail',
})

const resetComponent = () => {
    resetKey.value++
}

watch(
    () => route.query.socialCreditCode,
    (newQuery, oldQuery) => {
        console.log('>>>>>>', newQuery, oldQuery)
        resetComponent()
    }
)
</script>
<template>
    <Main :key="resetKey" />
</template>
<style lang="scss" scoped></style>
