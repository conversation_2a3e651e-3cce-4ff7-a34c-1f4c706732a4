<template>
    <div class="backCenter absolute border-radius-6 back-color-white display-flex top-bottom-center left-right-center">
        <van-icon name="location" color="#1989fa"  size="18" @click="backCenter()"/>
    </div>

    <div id="container"></div>
</template>

<script lang='ts' setup>
import AMapLoader from '@amap/amap-jsapi-loader'
import { ref, onMounted, defineEmits, computed, defineExpose } from 'vue'
import type { Ref } from 'vue'
import '@amap/amap-jsapi-types'
import mapLocation from '@/assets/hub-images/map/map-location.png'

interface marker {
    position: string
    title: string
    icon: string
}

interface markerData {
    lon: number
    lat: number
    companyName: string
}


const emits = defineEmits(['setMapInfo'])

const zoom: Ref<number> = ref(0)

const radius: Ref<number> = ref(2000)

const tm = ref(null)

const markers: Ref<marker[]> = ref([])

const labels: Ref<marker[]> = ref([])

const circles: Ref<marker[]> = ref([])

const model: Ref<string> = ref('normal')

const rangeCenter: Ref<string> = ref('')



const scale = computed((): number => {
    let scale = 0
    switch (parseInt(zoom.value)) {
    case 1:
        scale = 10000000
        break
    case 2:
        scale = 5000000
        break
    case 3:
        scale = 2000000
        break
    case 4:
        scale = 1000000
        break
    case 5:
        scale = 500000
        break
    case 6:
        scale = 200000
        break
    case 7:
        scale = 100000
        break
    case 8:
        scale = 50000
        break
    case 9:
        scale = 25000
        break
    case 10:
        scale = 20000
        break
    case 11:
        scale = 10000
        break
    case 12:
        scale = 5000
        break
    case 13:
        scale = 2000
        break
    case 14:
        scale = 1000
        break
    case 15:
        scale = 500
        break
    case 16:
        scale = 200
        break
    case 17:
        scale = 100
        break
    case 18:
        scale = 50
        break
    case 19:
        scale = 20
        break
    default:
        scale = 0
        break
    }
    return scale
})
const mapChange = () => {
    if (model.value !== 'range') {
        getCenter()
    }
}

const clearAll = () => {
    window.map.remove(labels.value)
    window.map.getAllOverlays('marker').forEach((overlay) => {
        window.map.remove(overlay)
    })
}

const addMarkers = (items: markerData[]) => {
    clearAll()
    console.log('addMarkers', items)

    markers.value = []
    items.forEach((item) => {
        let marker = new window.AMap.Marker({
            position: new window.AMap.LngLat(item.lon, item.lat),
            title: item.companyName,
            icon: new window.AMap.Icon({
                image: mapLocation, // 自定义图标的 URL
                imageSize: new window.AMap.Size(28, 40) // 设置图标实际显示的大小
            })

        })
        markers.value.push(marker)
    })
    window.map.add(markers.value)
}


const addCircle = (list) => {
    console.log('----=list', list)

    console.log('scale.value', scale.value)
    if (scale.value === 0) {
        return
    }
    circles.value = list.map((item) => {
        let lat = item.center.split(',')[1]
        let lon = item.center.split(',')[0]
        return {
            styleId: 'circle', //指定样式id
            center: new window.AMap.LngLat(lon, lat), //点标记坐标位置
            // radius: scale.value / 1700,
            id: item.id,
            name: item.name,
            count: item.count / 10000,
            code: item.code
        }
    })
    clearAll()
    circles.value.forEach((circle) => {
        let text = new window.AMap.Text({
            position: circle.center,
            anchor: 'bottom-center',
            text: circle.name + '<br>约' + circle.count + '万条',
            style: {
                'background-color': '#2B83FD',
                'font-size': '12px',
                'color': 'white',
                'text-align': 'center',
                'border-radius': '8px',
                'border': '1px solid #2B83FD',
                'display': 'flex',
                'align-items': 'center',
                'justify-content': 'center',
                'padding': '10px',
            },
            offset: [0, 15]
        })
        // console.log(text)
        labels.value.push(text)
        // circleMarker.on('click', circleCk)
        text.on('click', circleCk)
        // circleMarker.setMap(window.map)
        // window.map.add(text)
        text.setMap(window.map)
    })
}
const circleCk = (evt) => {
    console.log('circleCk', evt, [evt.lnglat.lng, evt.lnglat.lat])

    let toZoom = 0
    if (zoom.value > 10) {
        //区级
        toZoom = 13
    } else if (zoom.value > 6) {
        //市级
        toZoom = 11
    } else {
        //省级
        toZoom = 7
    }
    window.map.setZoomAndCenter(toZoom, [evt.lnglat.lng, evt.lnglat.lat])

}
const getCenter = async (params = null) => {
    if (tm.value) {
        clearTimeout(tm.value)
    }

    tm.value = setTimeout(async () => {
        let centerLocation = params?.customCenter || window.map.getCenter()
        // this.addMarkers([]);
        addMarkers([])
        zoom.value = window.map.getZoom()

        console.log('zoom.value', zoom.value)

        window.AMap.plugin('AMap.Geocoder', () => {
            var geocoder = new window.AMap.Geocoder()


            var lnglat = [centerLocation.lng, centerLocation.lat]

            console.log('lnglat', lnglat)
            geocoder.getAddress(lnglat, (status: string, result) => {
                console.log('-=-=-', status, result)
                if (status === 'complete' && result.info === 'OK') {
                    let addressInfo = result.regeocode.addressComponent

                    emits('setMapInfo', {
                        location: {
                            ad_info: { adcode: addressInfo.adcode },
                            location: {
                                lng: lnglat[0],
                                lat: lnglat[1]
                            }
                        },
                        zoom: zoom.value,
                        radius: params?.customCenter ? radius.value : null
                    })
                }
            })
        })
    }, 1000)
}
const backCenter = () => {
    window.map.setZoom(11)
    window.AMap.plugin(['AMap.Geolocation'], () => {
        let geolocation = new window.AMap.Geolocation({
            enableHighAccuracy: true, //是否使用高精度定位，默认:true
            timeout: 10000, //超过10秒后停止定位，默认：无穷大
            maximumAge: 0, //定位结果缓存0毫秒，默认：0
            convert: true, //自动偏移坐标，偏移后的坐标为高德坐标，默认：true
            showButton: true, //显示定位按钮，默认：true
            buttonPosition: 'RB', //定位按钮停靠位置，默认：'LB'，左下角
            buttonOffset: new window.AMap.Pixel(10, 20), //定位按钮与设置的停靠位置的偏移量，默认：Pixel(10, 20)
            showMarker: true, //定位成功后在定位到的位置显示点标记，默认：true
            showCircle: true, //定位成功后用圆圈表示定位精度范围，默认：true
            panToLocation: true, //定位成功后将定位到的位置作为地图中心点，默认：true
            zoomToAccuracy: true //定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
        })

        geolocation.getCurrentPosition((status: string, result: {
            position: {
                lat: number
                lng: number
                
            }
        }) => {
            console.log('定位结果', status, result)
            if (status === 'complete') {
                console.log('定位成功:', result.position)
                // 将地图中心设置为当前定位
                window.map.setCenter(result.position)
                getCenter()
            } else {

                console.error('定位失败:', result)
            }
        })
    })
}

const initMap = () => {

    if (window.map?.destroy) {
        window.map.destroy()
    }

    AMapLoader.load({
        key: '4fd1eb3675d6bede8f68d11db2292341',
        version: '2.0',
        plugins: ['AMap.ToolBar', 'AMap.Driving', 'AMap.Geocoder', 'AMap.CitySearch', 'AMap.CircleEditor'],
        AMapUI: {
            version: '1.1',
            plugins: []
        },
        Loca: {
            version: '2.0.0'
        }
    })
        .then((AMap) => {
            window.map = new AMap.Map('container', {
                viewMode: '2D',
                zooms: [3, 20],
                zoom: 12,
                resizeEnable: true
            })

            window.map.on('dragend', mapChange)
            window.map.on('zoomend', mapChange)

            backCenter()
        })
        .catch((e) => {
            console.log('地图加载错误', e)
        })
}



defineExpose({
    addCircle,
    model,
    rangeCenter,
    getCenter,
    addMarkers
})

onMounted(() => {
    initMap()
})

</script>

<style lang='scss' scoped>
#container {
    height: 100%;
    width: 100%;
    padding: 0px;
    margin: 0px;
    overflow: hidden;
    // z-index: 998; // 解决地图查找页面无法点击退出登录
}

.amap-icon {
    width: 25px !important;
    height: 34px !important;
}

.backCenter{
    width:40px;
    height:40px;
    z-index:998;
    right:5%;
    bottom:20%
}
</style>