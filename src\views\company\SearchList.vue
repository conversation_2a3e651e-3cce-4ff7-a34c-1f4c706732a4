<template>
    <div class="tb-padding-12 lr-padding-16 height-100 back-color-common">
        <van-sticky>
            <div class="search-box display-flex gap-10">
                <div class="flex-1">
                    <van-search
                        ref="searchRef"
                        v-model="searchKey"
                        placeholder="请输入企业名称进行查找"
                        @search="ckSearch"
                        @update:model-value="handleSearchKeyChange"
                    >
                    </van-search>
                </div>
            </div>
        </van-sticky>

        <div class="company-list">
            <van-list
                v-model="loading"
                :finished="finished"
                finished-text="暂无数据"
                :offset="100"
                :immediate-check="false"
                @load="loadMore"
            >
                <van-cell-group inset v-for="item in companyList" :key="item.name">
                    <template #title>
                        <div
                            class="tb-padding-12 lr-padding-8 back-color-white border-radius-8 t-margin-8"
                            @click="jump2Detail(item, 'companyDetail')"
                        >
                            <div class="display-flex gap-8">
                                <div
                                    class="w-46 h-46 border-radius-4 back-color-blue color-white font-14 font-weight-500 border-box lr-padding-8 flex-center"
                                >
                                    <span>
                                        {{ getShortName(item.name) }}
                                    </span>
                                </div>
                                <div class="flex-1 text-ellipsis tb-padding-8">
                                    <div
                                        class="font-16 text-ellipsis text-nowrap width-100 font-weight-600 color-black"
                                    >
                                        {{ item.name }}
                                    </div>
                                    <div class="t-margin-4">{{ item.socialCreditCode }}</div>
                                </div>
                            </div>
                            <div
                                class="display-flex t-margin-4 display-flex-nowrap gap-8 t-padding-8 border-bottom b-padding-14"
                            >
                                <div class="border-right r-padding-8 flex-1 text-center">
                                    <div>法定代表人</div>
                                    <div class="color-blue t-margin-4">{{ item.legalperson }}</div>
                                </div>
                                <div class="border-right r-padding-8 flex-1 text-center">
                                    <div>注册资本</div>
                                    <div class="t-margin-4">{{ item.regCapDisplay }}</div>
                                </div>
                                <div class="flex-1 text-center">
                                    <div>成立日期</div>
                                    <div class="t-margin-4">
                                        {{ item.esdate ? $moment(item.esdate).format('YYYY-MM-DD') : '-' }}
                                    </div>
                                </div>
                            </div>
                            <div class="display-flex t-margin-8 gap-8 flex-end">
                                <div class="light-btn" v-if="!oemInfo?.easyCollect">
                                    <icon icon="icon-Frame-1" :size="16" color="#3C74EB" /> 体检
                                </div>
                                <div class="light-btn" @click.stop="jump2Report(item)">
                                    <icon icon="icon-Frame4" :size="16" color="#3C74EB" />认证
                                </div>
                            </div>
                        </div>
                    </template>
                </van-cell-group>
            </van-list>
            <div v-if="loading === true" class="t-margin-12">
                <img src="@/assets/images/search-company-pic.png" alt="" width="100%" />
            </div>
            <van-empty
                v-if="!companyList.length && loading === false"
                :image="noDataImg"
                image-size="224"
                description="暂无数据"
            />
        </div>
    </div>

    <CollectPopup ref="collectPopup" />
</template>

<script setup lang="ts">
import { showNotify } from 'vant'
import { ref, computed, onActivated } from 'vue'
import type { Ref } from 'vue'
import type { SearchInstance } from 'vant'

import type { ICompanyInfo } from '@/types/company'

import { useRouter, useRoute } from 'vue-router'

import { setItem, getItem } from '@/utils/storage.ts'

import aicService from '@/service/aicService'

import crmService from '@/service/crmService'

import noDataImg from '@/assets/images/no-data.png'

import { useStore } from 'vuex'
import type { RootState } from '@/types/store'

import CollectPopup from '@/components/collect/CollectPopup.vue'

defineOptions({
    name: 'searchCompany',
})

const router = useRouter()

const route = useRoute()

const searchKey: Ref<string> = ref('')

const companyList: Ref<ICompanyInfo[]> = ref([])

const loading: Ref<boolean> = ref(false)
const finished: Ref<boolean> = ref(false)

const searchRef = ref<SearchInstance>()

const pageInfo: Ref<{ page: number; pageSize: number; total: number }> = ref({
    page: 1,
    pageSize: 10,
    total: 0,
})
const store = useStore<RootState>()
const oemInfo = computed(() => {
    const { oemConfig } = store.state.auth || {}
    const { modules } = oemConfig || {}
    const { config } = modules?.[0] || {}
    return config
})

onActivated(() => {
    if (route.query.searchKey && route.query.searchKey !== searchKey.value) {
        searchKey.value = route.query.searchKey as string
        companyList.value = []
        pageInfo.value.page = 1
        search()
    }
    if (!route.query.searchKey) {
        searchRef.value?.focus()
    }
})

const handleSearchKeyChange = (val: string) => {
    if (val.length > 1) {
        ckSearch()
    }
}

const search = () => {
    if (!searchKey.value) {
        return
    }
    loading.value = true
    finished.value = false
    let cacheRes = getItem('historySearch') || []

    const index = cacheRes.indexOf(searchKey.value)

    if (index === -1) {
        // 如果不存在，添加到数组最前面
        cacheRes.unshift(searchKey.value)
        // 保证数组长度不超过10
        if (cacheRes.length > 10) {
            // cacheRes.shift() // 删除最旧的元素
            cacheRes.pop() // 删除数组的最后一个元素
        }
    } else {
        // 如果已经存在，将元素移到数组第一位
        cacheRes.splice(index, 1) // 移除旧的元素
        cacheRes.unshift(searchKey.value) // 添加到数组首位
    }

    setItem('historySearch', cacheRes)
    aicService
        .searchEnterprise({
            keyword: searchKey.value,
            scope: '0',
            pageSize: 10,
            page: pageInfo.value.page,
        })
        .then((res) => {
            console.log(res)
            companyList.value = companyList.value.concat(res.data)
            loading.value = false
        })
        .finally(() => {})
}

const ckSearch = () => {
    pageInfo.value.page = 1
    companyList.value = []
    search()
}

const loadMore = () => {
    pageInfo.value.page += 1
    search()
}

const jump2Detail = (item: ICompanyInfo, routerName: string) => {
    const { easyCollect } = oemInfo.value || {}
    if (easyCollect) return
    let data = {
        socialCreditCode: item.socialCreditCode,
        companyName: item.companyName,
    }

    crmService.gsGetCompanyClueInfo({ socialCreditCode: item.socialCreditCode }).then((getCrmUserRes) => {
        let res = getCrmUserRes.data

        if (res.clueType) {
            //已经转线索的，刷新
            crmService
                .refresh({
                    socialCreditCode: item.socialCreditCode,
                    companyName: item.companyName,
                    leadId: res.leadId,
                })
                .then(() => {
                    router.push({
                        name: routerName,
                        query: {
                            socialCreditCode: item.socialCreditCode,
                            companyName: item.companyName || item.name,
                        },
                    })
                })
        } else {
            //未转的，转线索
            crmService
                .crmAdd({
                    clueType: 2,
                    source: 13,
                    ...data,
                })
                .then((res) => {
                    if (res.errCode) {
                        showNotify({ type: 'danger', message: res.errMsg })
                        return
                    }
                    router.push({
                        name: routerName,
                        query: {
                            socialCreditCode: item.socialCreditCode,
                            companyName: item.companyName || item.name,
                        },
                    })
                })
        }
    })
}

const jump2Report = (item: ICompanyInfo) => {
    console.log('item123123', item)
    // 没转线索的先转线索

    let data = {
        socialCreditCode: item.socialCreditCode,
        companyName: item.companyName,
    }
    crmService.gsGetCompanyClueInfo({ socialCreditCode: item.socialCreditCode }).then((getCrmUserRes) => {
        let res = getCrmUserRes.data
        if (res.clueType) {
            //已经转线索的，刷新
            crmService
                .refresh({
                    socialCreditCode: item.socialCreditCode,
                    companyName: item.companyName,
                    leadId: res.leadId,
                })
                .then(() => {
                    afterAddCrm(item)
                })
        } else {
            //未转的，转线索
            crmService
                .crmAdd({
                    clueType: 2,
                    source: 13,
                    ...data,
                })
                .then((res) => {
                    if (res.errCode) {
                        showNotify({ type: 'danger', message: res.errMsg })
                        return
                    }
                    afterAddCrm(item)
                })
        }
    })
}

const collectPopup = ref()

const afterAddCrm = (item: ICompanyInfo) => {
    const { easyCollect } = oemInfo.value || {}

    let info = {
        socialCreditCode: item.socialCreditCode,
        companyName: item.companyName,
        deductType: 'fpbg',
    }

    if (easyCollect) {
        collectPopup.value.getCollectUrl(info)
    } else {
        router.push({
            name: 'companyReport',
            query: {
                socialCreditCode: item.socialCreditCode,
                companyName: item.companyName,
            },
        })
    }
}

const getShortName = (str: string) => {
    const pattern = /([\u4e00-\u9fa5]{2,}省)/
    const res = str.replace(pattern, '')
    const provinces = [
        '北京',
        '天津',
        '河北',
        '山西',
        '辽宁',
        '吉林',
        '黑龙江',
        '上海',
        '江苏',
        '浙江',
        '安徽',
        '福建',
        '江西',
        '山东',
        '河南',
        '湖北',
        '湖南',
        '广东',
        '海南',
        '四川',
        '贵州',
        '云南',
        '陕西',
        '甘肃',
        '青海',
        '台湾',
        '内蒙古',
        '广西',
        '西藏',
        '宁夏',
        '新疆',
        '香港',
        '澳门',
        '重庆',
    ]

    // 按长度降序排序，确保优先匹配长名称
    provinces.sort((a, b) => b.length - a.length)

    // 构建正则表达式
    const regex = new RegExp(
        `^(${provinces
            .map((p) =>
                // 处理特殊字符并添加边界匹配
                p.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
            )
            .join('|')})`
    )

    // 去除匹配到的省级行政区划
    const cleanedStr = res.replace(regex, '')

    const patterncity = /([\u4e00-\u9fa5]{2,}市)/
    const cleanedStringCity = cleanedStr.replace(patterncity, '')
    // 返回前四个字符
    return cleanedStringCity.slice(0, 4)
}
</script>

<style scoped>
:deep(.van-search__content) {
    background-color: #fff;
}

.search-box {
    --van-search-padding: 0;
}

.logo-box {
    width: 0.8rem;
    min-width: 0.8rem;
    height: 0.8rem;
    /* line-height: 25px; */
    padding: 5px;
    letter-spacing: 4px;
}

.company-list {
    --van-cell-group-inset-title-padding: 0px;
}
</style>
