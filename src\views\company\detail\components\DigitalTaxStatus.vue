<script lang="ts" setup>
import DigitalTaxStatusDialog from '@/components/enterprise/list/DigitalTaxStatusDialog.vue'
import aicService from '@/service/aicService'
import { onMounted, ref } from 'vue'

const props = defineProps<{
    socialCreditCode: string
    name: string
}>()

const statusStr = ref('')
const status = ref<number | null>(null)
const loading = ref(false)
const showDialog = ref(false)

const getData = () => {
    loading.value = true
    aicService
        .searchCheckActivate({
            socialCreditCode: props.socialCreditCode,
        })
        .then((res) => {
            loading.value = false
            const { errCode, data } = res
            if (errCode === 0) {
                statusStr.value = data.statusStr
                status.value = data.status
            }
        })
}

onMounted(() => {
    getData()
})
</script>
<template>
    <div class="flex flex-row back-color-white all-padding-12 gap-12 space-between">
        <van-skeleton title class="flex-1" v-if="loading" />
        <div class="font-16 color-black font-weight-500" v-if="!loading">数电发票</div>

        <div
            class="font-14 lh-12 flex flex-row color-text-grey top-bottom-center gap-2"
            v-if="!loading"
            @click="
                () => {
                    showDialog = true
                }
            "
        >
            <div>{{ statusStr }}</div>
            <van-icon name="arrow" />
        </div>
    </div>
    <van-dialog
        v-model:show="showDialog"
        title="数电发票业务"
        :showConfirmButton="false"
        :destroyOnClose="true"
        :closeOnClickOverlay="true"
        :close="() => (showDialog = false)"
    >
        <DigitalTaxStatusDialog
            :socialCreditCode="socialCreditCode"
            :name="name"
            :close="() => (showDialog = false)"
            v-if="showDialog"
        />
    </van-dialog>
</template>
<style scoped lang="scss"></style>
