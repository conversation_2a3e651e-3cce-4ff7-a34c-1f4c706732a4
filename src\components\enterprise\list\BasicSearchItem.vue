<script lang="ts" setup>
import type { ICompanyInfo, ICompanyTag } from '@/types/company'
import CICON from '@/assets/hub-images/company/company-icon.png'
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import Icon from '@/components/common/Icon.vue'

const props = defineProps<{
    data: ICompanyInfo
    openActionSheet: (companyInfo: ICompanyInfo) => void
    scope: string
    toJyhy: (companyInfo: ICompanyInfo) => void
    openTaxDialog: (companyInfo: ICompanyInfo) => void
}>()

const router = useRouter()

const isCantransfer = computed(() => {
    return props.data.clueInfo?.clueType === 0
})

const isShowBaseInfo = computed(() => {
    return props.scope !== 'personnel'
})

const isShowIntro = computed(() => {
    return props.scope !== 'personnel'
})

const isShowPersonnelInfo = computed(() => {
    return props.scope === 'personnel'
})

const isShowSite = computed(() => {
    return props.scope === 'personnel'
})

const isShowAddress = computed(() => {
    return props.scope === 'personnel'
})

const toJyhyUrl = (data: ICompanyInfo) => {
    props.toJyhy(data)
}

const toDetail = () => {
    router.push({
        name: 'company-detail',
        query: { socialCreditCode: props.data.socialCreditCode },
    })
}

const formatTags = (tgas: ICompanyTag[]) => {
    if (!tgas) return []
    try {
        return tgas.filter((e) => e.categoryCode !== '007')
    } catch (error) {
        console.log(error)
        return []
    }
}
</script>

<template>
    <div class="flex flex-column back-color-white all-padding-12 border-radius-8 gap-8" @click="toDetail">
        <div class="flex flex-row gap-8">
            <div class="h-48 w-48">
                <img :src="CICON" alt="" class="height-100 wight-100" />
            </div>
            <div class="flex flex-column gap-4">
                <div class="font-16 color-black" v-html="data.companyname_ws"></div>
                <div class="font-16 flex flex-row gap-4 oh">
                    <template v-for="(value, index) in formatTags(data.companyTags)" :key="index">
                        <div class="small-tag tag-green" v-if="value.categoryCode === '001'">
                            {{ value.tagName }}
                        </div>
                        <div class="small-tag tag-blue" v-if="index < 3 && value.categoryCode !== '001'">
                            {{ value.tagName }}
                        </div>
                    </template>
                    <div class="small-tag tag-blue" v-if="formatTags(data.companyTags).length > 3">...</div>
                </div>
            </div>
        </div>
        <div class="font-14" v-if="isShowBaseInfo">
            <span class="color-blue">{{ data.legalperson || '-' }}</span
            >｜{{ data.regCapDisplay || '-' }}｜{{ data.esdate || '-' }}
        </div>
        <div class="font-14 text-ellipsis text-nowrap" v-if="isShowIntro">简介：{{ data.opscope || '-' }}</div>
        <div
            class="font-14 text-ellipsis text-nowrap remove-em color-blue"
            v-html="data.personnel_info || '-'"
            v-if="isShowPersonnelInfo"
        ></div>

        <div class="font-14 text-ellipsis text-nowrap" v-if="isShowSite">网址：{{ data.officialWebsite || '-' }}</div>
        <div class="font-14" v-if="isShowAddress">通讯地址：{{ data.contactaddress || '-' }}</div>
        <div class="h-1 back-color-border width-100"></div>
        <div class="flex flex-row font-14 space-around color-blue actions">
            <div
                class="flex flex-row left-right-center top-bottom-center gap-4 flex-1 right-border"
                @click.stop="toJyhyUrl(data)"
            >
                <Icon icon="icon-tijian" size="16" color="main-blue" />
                体检
            </div>
            <div
                class="flex flex-row left-right-center top-bottom-center gap-4 flex-1 right-border"
                @click.stop="openTaxDialog(data)"
            >
                <Icon icon="icon-shudian" size="16" color="main-blue" />
                数电
            </div>
            <div
                class="flex flex-row left-right-center top-bottom-center gap-4 flex-1 right-border"
                v-if="isCantransfer"
                @click.stop="openActionSheet(data)"
            >
                <Icon icon="icon-zhuanyii" size="16" color="main-blue" />
                转移
            </div>
        </div>
    </div>
</template>

<style scoped>
.right-border:not(:last-of-type) {
    border-right: 1px solid var(--border-color);
}
</style>
