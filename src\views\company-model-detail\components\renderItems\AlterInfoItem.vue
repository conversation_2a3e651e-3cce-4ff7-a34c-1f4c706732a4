<template>
    <div class="wrapper flex flex-column row-gap-10 b-margin-10">
        <Items
            class="flex font-16 lh-18 gap-10"
            :columns="excludeChanges || []"
            :page_config="page_config"
            :row="row"
            :channelType="channelType"
            :modelName="modelName"
        />
        <div style="background-color: #f6f7fc" class="relative font-16 all-padding-10 border-radius-10">
            <div
                class="flex gap-20 change-wrapper"
                ref="contentRef"
                :style="{
                    maxHeight: isExpanded ? 'none' : `${CONTENT_HEIGHT}px`,
                    overflow: isExpanded ? 'visible' : 'hidden',
                }"
            >
                <div v-if="alterBefore" class="flex-1">
                    <div class="label color-two-grey">{{ alterBefore.name }}</div>
                    <div v-html="alterBefore.render ? alterBefore.render(row) : getValue(alterBefore.key)"></div>
                </div>
                <div v-if="alterAfter" class="flex-1">
                    <div class="label color-two-grey">{{ alterAfter.name }}</div>
                    <div v-html="alterAfter.render ? alterAfter.render(row) : getValue(alterAfter.key)"></div>
                </div>
            </div>
            <div v-if="showArrow" style="height: 20px" @click="isExpanded = !isExpanded">
                <div class="expand">
                    <div v-if="!isExpanded" class="mask"></div>
                    <van-icon
                        style="color: var(--main-blue-)"
                        :name="isExpanded ? 'arrow-up' : 'arrow-down'"
                        class="arrow-icon"
                    />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { RequestKeys, type PageConfigItem } from '../../config'
import Items from './Items.vue'
import { getNestedValue } from '../../utils'

const { page_config, row, channelType, modelName } = defineProps<{
    page_config: PageConfigItem
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    row: any
    channelType: number
    modelName: RequestKeys
}>()

const excludeChanges = computed(() => {
    return page_config.columns?.filter((item) => !['alterBefore', 'alterAfter'].includes(item.key))
})

const alterBefore = computed(() => {
    return page_config.columns?.filter((item) => item.key === 'alterBefore')?.[0]
})

const alterAfter = computed(() => {
    return page_config.columns?.filter((item) => item.key === 'alterAfter')?.[0]
})
const getValue = (key: string) => {
    return getNestedValue(row, key)
}

const isExpanded = ref(false)
const contentRef = ref<HTMLDivElement>()
const showArrow = ref(false)

const CONTENT_HEIGHT = 60 // 指定高度阈值
// 检查内容高度是否超过阈值
const checkContentHeight = () => {
    if (contentRef.value) {
        showArrow.value = contentRef.value.scrollHeight > CONTENT_HEIGHT

        // 如果内容高度小于阈值，强制收起
        if (contentRef.value.scrollHeight <= CONTENT_HEIGHT) {
            isExpanded.value = false
        }
    }
}

// 监听内容变化和DOM加载完成时检查高度
onMounted(checkContentHeight)
watch(() => row, checkContentHeight, { deep: true })
</script>

<style scoped lang="scss">
.change-wrapper {
    position: relative;
    box-sizing: border-box;
    transition: max-height 0.3s ease;
    position: relative;
    &::before {
        content: ' ';
        position: absolute;
        height: 100%;
        width: 1px;
        left: 50%;
        top: 0;
        background-color: #e5e5e5;
    }
}
.expand {
    padding: 10px;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    box-sizing: border-box;
}
.mask {
    position: absolute;
    top: -20px;
    left: 0;
    width: 100%;
    height: 40px;
    background: linear-gradient(to top, #f6f7fc 0%, transparent 100%);
    pointer-events: none; /* 让点击事件穿透遮罩 */
}
</style>
