<template>
    <div class="relative height-100">
        <div class="search-bar absolute top-10 display-flex gap-10" style="z-index: 1000;padding:0 5px;">
            <div class="back-color-white font-14 display-flex top-bottom-center border-bottom lr-padding-8 height-100 border-radius-6"
                 style="width: calc(100% - 80px);">
                <div @click="showScopeFlag = !showScopeFlag" style="width:85px">
                    {{ searchParams.scope === 'address' ? '地址定位' : '企业名称' }}
                    <van-icon :name="!showScopeFlag ? 'arrow-down' : 'arrow-up'" />
                </div>
                <div class="flex-1"><van-search v-model="searchParams.keyword" @search="querySearch"
                                                :placeholder="searchParams.scope === 'address' ? '输入地址为您推荐附近的企业' : '请输入企业名称'" /></div>

            </div>
            <MapFilterPopup @filterChange="filterChange" />



        </div>



        <van-popup v-model:show="showScopeFlag" round position="bottom">
            <van-radio-group v-model="searchParams.scope">
                <van-cell-group>
                    <van-cell title="地址定位" clickable @click="scopeChange()">
                        <template #right-icon>
                            <van-radio name="address" />
                        </template>
                    </van-cell>
                    <van-cell title="企业名称" clickable @click="scopeChange()">
                        <template #right-icon>
                            <van-radio name="companyname" />
                        </template>
                    </van-cell>
                </van-cell-group>
            </van-radio-group>
        </van-popup>


        <van-popup v-model:show="showKeyWordFlag" :overlay="false" round position="top"
                   :style="{ marginTop: '88px', width: '63%', marginLeft: '80px' }">

            <van-cell v-for="item in tempSearchCompanyList" :key="item.name" :title="item.name" clickable
                      @click="handleSelect(item)">

            </van-cell>

        </van-popup>

        <Map ref="map" @setMapInfo="setMapInfo" />
        <van-floating-panel v-model:height="panelHeight" :anchors="anchors" :content-draggable="false"
                            v-show="searchParams.keyword || zoomLevel === 4">
            <CompanySearchList :load-data="() => { }" :data="companyList" :loading="loadingCompanyList"
                               :total="listTotal" :finished="true" :disRefresh="true" />
        </van-floating-panel>
    </div>



</template>

<script lang='ts' setup>
import {
    ref, onMounted, reactive, computed,
    //  getCurrentInstance
} from 'vue'

import type { Ref } from 'vue'
import Map from './components/Map.vue'
import CompanySearchList from '@/components/enterprise/list/CompanySearchList.vue'
import MapFilterPopup from './components/MapFilterPopup.vue'
import aicService from '@/service/aicService'

const companyList = ref([])

const listTotal = ref(0)
const zoom: Ref<number> = ref(0)


const map = ref(null)


// const industry = ref(null)


const showScopeFlag = ref(false)


const showKeyWordFlag = ref(false)

const anchors = [
    100,
    Math.round(0.5 * window.innerHeight),
    Math.round(0.7 * window.innerHeight),
]
const panelHeight = ref(anchors[0])


const searchParams = reactive({
    scope: 'address',
    keyword: '',
    page: 1,
    pageSize: 20,
    filter: {
        syncRobotRangeDate: [],
        location: [],
        industryshort: [],
        secindustryshort: [],
        circle: null,
        filterSync: 0,
        filterUnfold: 0,
        filterSyncRobot: 0,
        fourthIndustryShort: [],
        thirdIndustryShort: [],
        contact: '', // 联系方式
        entstatus: '', // 营业状态
        establishment: [],
        registercapital: '', // 注册资金
        enttype: [],
        industry: []
    }
})


const filterChange = (params) => {

    searchParams.filter = { ...searchParams.filter, ...params }
    searchParams.page = 1
    getLxyData()

}



const zoomLevel = computed(() => {
    let v = zoom.value
    if (v > 12) {
        //街道
        return 4
    } else if (v > 10) {
        //区级
        return 3
    } else if (v > 6) {
        //市级
        return 2
    } else {
        //省级
        return 1
    }
})
const setMapInfo = (params) => {
    zoom.value = params.zoom
    console.log(params)
    let code = params.location.ad_info.adcode
    let location = params.location.location
    let codes = [code.slice(0, 2), code.slice(0, 4)]
    if (zoom.value > 12) {
        codes.push(code)
    }
    console.log(zoomLevel)

    searchParams.filter.location = codes.slice(0, zoomLevel.value - 1)
    searchParams.filter.circle = {
        center: `${location.lat},${location.lng}`,
        radius: params.radius ? parseInt(params.radius) / 1000 : 5
    }

    // this.$refs['list'].$data.page = 1;

    // enterpriseMapInfoList.value.pageInfo.page = 1

    searchParams.page = 1

    getLxyData()
}

const searchData = computed(() => {
    let obj = JSON.parse(JSON.stringify(searchParams))
    let filter = obj.filter
    let model = map.value?.model || ''
    //将自己数据格式转换成励晓云的数据
    //转换地址，取最后一位
    filter.location = [filter.location[filter.location.length - 1]]
    if (zoomLevel.value === 1) {
        filter.location = ['0']
    }
    if (filter.industry.length) {
        filter.industry = [filter.industry]
    }
    if (filter.establishment?.length && filter.establishment.length === 2) {
        filter.establishment = [`${filter.establishment[0]}-${filter.establishment[1]}`]
    } else {
        filter.establishment = []
    }
    if (filter.contact) {
        filter.contact = [filter.contact]
    } else {
        filter.contact = ['0']
    }
    if (filter.entstatus) {
        filter.entstatus = [filter.entstatus]
    } else {
        filter.entstatus = ['0']
    }
    if (filter.registercapital) {
        filter.registercapital = [filter.registercapital]
    } else {
        filter.registercapital = ['0']
    }

    if ((zoomLevel.value <= 3 || obj.scope === 'companyname') && model !== 'range') {
        obj.filter.circle = null
    }


    return obj

})

let tm = null

const formatNumber = (num, type) => {
    num = Number(num)
    if (num === 0) {
        return num + ''
    } else if (type === 'distance') {
        return (num / 1000).toFixed(2)
    } else {
        if (num > 1 && num < 10000) {
            return num + ''
        } else {
            return (num / 10000).toFixed(2) + '万'
        }
    }
}

const loadingCompanyList = ref(false)

const companyListFinished = ref(false)
const getLxyData = () => {
    // 开始调用接口获取数据
    if (tm) {
        clearTimeout(tm)
    }
    tm = setTimeout(() => {
        let model = map.value.model
        let data = JSON.parse(JSON.stringify(searchData.value))

        data = { ...data, ...data.filter }

        delete data.filter
        console.log('data', data)

        if (zoomLevel.value === 4 || searchParams.keyword || model === 'range') {
            //区级调用公司接口
            loadingCompanyList.value = true

            aicService.searchCenterEnt(data).then((res) => {

                console.log(res)
                let list = res.data

                map.value.addMarkers(list.filter(lo => { return lo.lat }))

                let centerInfo = null
                if (model === 'range') {

                    centerInfo = map.value.rangeCenter
                } else {
                    centerInfo = window.map.getCenter()
                }
                if (!centerInfo) {
                    return
                }
                let center = new window.AMap.LngLat(centerInfo.lng, centerInfo.lat)
                list.forEach((item) => {
                    if (item.lon && item.lat) {
                        let p1 = [parseFloat(item.lon), parseFloat(item.lat)]
                        let distance = Math.round(window.AMap.GeometryUtil.distance(center, p1))
                        item.toCenterDistance = formatNumber(distance, 'distance')
                    }
                })

                companyList.value = list

                // this.listResult = list;
                listTotal.value = res.total > 20 ? 20 : res.total

                companyListFinished.value = !res.hasNextPage
            }).finally(() => {
                loadingCompanyList.value = false
            })
        } else {
            // 其余调用统计接口
            aicService.searchStatistics(data).then(res => {
                console.log(res)
                map.value.addCircle(res.data)
            })
        }
    }, 200)
}

const scopeChange = () => {
    searchParams.keyword = ''
    let lo = searchParams.filter.location
    searchParams.filter.location = lo.slice(0, 2)

    showScopeFlag.value = false
}

const getCompanyName = async () => {
    let data = JSON.parse(JSON.stringify(searchData.value))
    data = { ...data, ...data.filter, pageSize: 5 }
    delete data.filter

    console.log('getCompanyName', data)
    let res = await aicService.searchCenterEnt(data)
    console.log(res)
    let arr = res.data.map((item) => {
        return { ...item, value: item.name }
    })
    tempSearchCompanyList.value = arr

    showKeyWordFlag.value = true
}

const getMapAddress = (query) => {
    window.map.plugin(['AMap.AutoComplete'], () => {
        var autoOptions = { city: searchParams.filter.location[0] }
        var autoComplete = new window.AMap.AutoComplete(autoOptions)
        autoComplete.search(query, (status, res) => {
            console.log(status, res)
            if (status === 'complete') {
                console.log('res.tips', res.tips)

                let arr = res.tips.map((item) => {
                    return { ...item, value: item.name, lat: item.location.lat, lon: item.location.lng }
                })
                tempSearchCompanyList.value = arr
            } else {
                tempSearchCompanyList.value = []
            }
            showKeyWordFlag.value = true
        })
    })
}

const handleSelect = (e) => {
    console.log('eee', e)
    //将地图移动到点选的位置
    searchParams.keyword = e.value
    window.map.setCenter(new window.AMap.LngLat(e.lon, e.lat), true)
    map.value.getCenter()
    if (searchParams.scope === 'companyname') {
        window.map.setZoom(11)
        getLxyData()
    } else {
        window.map.setZoom(15)
    }
    showKeyWordFlag.value = false
}

// const choseLoaction = (val) => {
//     let list = cityRef.value.getCheckedNodes()[0].pathLabels
//     console.log('list', list)
//     let str = list[0] + '省'
//     if (list[1]) {
//         str += list[1] + '市'
//     }
//     if (list[2]) {
//         str += list[2] + '区'
//     }

//     window.map.plugin(['AMap.AutoComplete'], () => {
//         var autoOptions = { city: searchParams.filter.location[0] }
//         var autoComplete = new window.AMap.AutoComplete(autoOptions)
//         autoComplete.search(str, (status, res) => {
//             console.log('status', status)
//             if (status === 'complete') {
//                 console.log('res.tips', res.tips)

//                 let arr = res.tips.map((item) => {
//                     return { ...item, value: item.name, lat: item.location.lat, lon: item.location.lng }
//                 })
//                 let e = arr[0]
//                 console.log('e', e)
//                 window.map.setCenter(new window.AMap.LngLat(e.lon, e.lat))
//                 if (val.length === 3) {
//                     window.map.setZoom(13)
//                 } else if (val.length === 2) {
//                     window.map.setZoom(12)
//                 } else {
//                     window.map.setZoom(7)
//                 }
//             }
//         })
//     }, () => {
//         console.log('err')
//     })
// }


const tempSearchCompanyList = ref([])
const querySearch = async (query) => {
    if (tm) {
        clearTimeout(tm)
    }
    tm = setTimeout(() => {
        // this.$refs['list'].$data.page = 1;
        searchParams.page = 1
        if (searchParams.scope === 'companyname') {
            //公司名称搜索
            searchParams.filter.location = searchParams.filter.location.map(lo => { return lo.slice(0, 4 + '00') })
            getCompanyName(query)
        } else {
            //地址搜索，调用地图接口
            getMapAddress(query)
        }
    }, 200)
}

// const changeAll = (val: number[]) => {
//     let lastVal = val[val.length - 1]

//     if (lastVal === 0) {
//         searchParams.filter.enttype = [0]
//     } else {
//         searchParams.filter.enttype = searchParams.filter.enttype.filter((v) => { return v !== 0 })
//     }
// }

// const pageChange = (page: number) => {
//     searchParams.page = page
//     getLxyData()
// }




onMounted(() => {
})


</script>


<style scoped>
#container {
    width: 100%;
    height: 100%;
}

.search-bar {
    height: 40px;
    width: calc(100%);

    --van-search-padding: 0;
    --van-search-content-background: none;
    --van-padding-sm: 0px;
}
</style>