<template>
    <div class="height-100 back-color-common display-flex flex-column space-between lr-padding-16">
        <div class="display-flex flex-column top-bottom-center">
            <img class="w-36 h-32 t-margin-40" src="@/assets/images/zqy-logox2.png" alt="臻企云logo" />
            <div class="font-16 font-weight-500 color-black t-margin-17">{{ name }} 赠送您</div>
            <div
                class="t-margin-25 width-100 border-radius-8 tb-padding-24 border-color-blue"
                style="background: linear-gradient(99.4deg, #cadbff 2.86%, #e7f7ff 101.71%)"
            >
                <div class="font-20 font-weight-500 !color-blue text-center">
                    “{{ service }}”X{{ quantity }}
                </div>
                <!-- <div class="font-12 !color-blue text-center t-margin-4">
                    有效时间：{{ serviceItemInfo?.exTime }}
                </div> -->
                <div class="display-flex top-bottom-center font-14 !color-blue t-margin-4 center">
                    <div>请在&nbsp;</div>
                    <van-count-down :time="countDownTime" format="HH:mm:ss">
                    </van-count-down>
                    <div>&nbsp;内领取</div>
                </div>

            </div>
        </div>
        <div class="b-margin-12">
            <van-button
                v-if="isExpire"
                type="primary"
                class="h-46 width-100 border-radius-8"
                style="background: linear-gradient(to right, #9ebaf5, #caeaf9); border: none"
                disabled
            >
                {{ '权益已过期 ' }}
            </van-button>
            <van-button
                v-else-if="isReceive"
                type="primary"
                class="h-46 width-100 border-radius-8"
                style="background: linear-gradient(to right, #9ebaf5, #caeaf9); border: none"
                disabled
                @click="isReceive ? receiveBenefit : null"
            >{{ '权益已被领取' }}
            </van-button>
            <van-button
                v-else
                type="primary"
                class="h-46 width-100 border-radius-8"
                style="background: linear-gradient(99.4deg, #3C74EB 2.86%, #95D5F4 101.71%);border: none;"
                @click="receiveBenefit"
            >{{ '立即领取' }}
            </van-button>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { onMounted, ref, computed, onBeforeMount } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import { showToast } from 'vant'
import orderService from '@/service/orderService'
import { useRouter } from 'vue-router'

const router = useRouter()
const store = useStore<RootState>()
const userInfo = computed(() => {
    const { account } = store.state.user
    const { user } = account || {}
    return user
})
const route = useRoute()
const id = ref('')
const isReceive = ref(false)
const name = ref('')
const quantity = ref(0)
const exTime = ref(0) // 到期时间
const service = ref('')
const serviceName = (key: string) => {
    if(key === 'xs'){
        return '线索联系方式'
    }
    else if(key === 'swbg'){
        return '企业财税经营分析报告'
    }
    else if(key === 'gqbg'){
        return '高新技术科技企业报告'
    }
    else if(key === 'fpbg'){
        return '企业发票数据综合分析报告'
    }else if(key === 'znwh'){
        return '智能外呼'
    }else{
        return '-'
    }
}
const currentTime = ref(new Date().getTime()) // 获取当前时间的时间戳（毫秒）
const countDownTime = computed(() => {
    if (exTime.value && exTime.value>currentTime.value) {
        console.log('exTime', exTime.value)
        console.log('currentTime.value', currentTime.value)
        console.log('exTime.value - currentTime.value', exTime.value - currentTime.value)
        return (exTime.value - currentTime.value)
    } else {
        return 0
    }
})
const isExpire = computed(() => {
    if (currentTime.value > exTime.value) {
        return true
    } else {
        return false
    }
})

const transIds = ref('')
const init = () => {
    id.value = encodeURIComponent(route.query.id as string)
    orderService.orderGetServiceItemInfo({ id: id.value }).then((res) => {
        const { sendUserName, prePayAmount, expireTime, isPrePayOrder, transId, serviceKey } = res.data
        name.value = sendUserName
        quantity.value = prePayAmount
        exTime.value = expireTime
        transIds.value = transId
        service.value = serviceName(serviceKey)
        if (isPrePayOrder === 0) {
            isReceive.value = true
        }
    })
}

const receiveBenefit = () => {
    if (transIds.value) {
        const confirmParams = {
            transId: transIds.value,
            uuid:encodeURIComponent(route.query.uuid as string)
        }
        console.log('confirmParams', confirmParams)
        // if(!userInfo.value){
        //     showToast({
        //         message: '用户未登录,即将跳转登录页',
        //         duration: 1000,
        //     })
        //     setTimeout(() => {
        //         router.push('/phone-login')
        //     },1000)
        // }else{
        sessionStorage.setItem('claimBenefit', route.fullPath)

        orderService.orderConfirm(confirmParams).then((res) => {
            console.log('领取权益结果:', res)
            if (res.success) {
                showToast({
                    message: '领取成功',
                })
                router.push({
                    name: 'home',
                })
            } else
                showToast({
                    message: res.errMsg,
                })
        })
        // }
    }
}
onBeforeMount(() => {
    console.log('userInfo', userInfo.value)
})
onMounted(() => {
    init()
})
</script>

<style scoped lang="scss">
:deep(.van-count-down ){
    color: var(--main-blue-);
}
</style>
