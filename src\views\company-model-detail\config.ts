import { formatDate } from './utils'

export enum ColumnType {
    Text = 'Text',
    RelateCompanyLink = 'RelateCompanyLink',
    CompanyProfileLink = 'CompanyProfileLink',
}

export interface Column {
    name: string
    key: string
    type: ColumnType
    groupIndex?: number // 分组索引，相同索引的列将在同一行显示
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    render?: (rowData: any) => string
    textCollapse?: boolean //文本组件是否可折叠
    defaultExpanded?: boolean //文本组件是否默认展开
}
export enum RequestKeys {
    GSInfo = 'GSInfo',
    EntInfo = 'EntInfo',
    Shareholder = 'Shareholder',
    Investment = 'Investment',
    BaikeInfo = 'BaikeInfo',
    KeyPerson = 'KeyPerson',
    AlterInfo = 'AlterInfo',
    Branch = 'Branch',
    AnnualReport = 'AnnualReport',
    EnterpriseProductInfo = 'EnterpriseProductInfo',
    OriginateTeam = 'OriginateTeam',
    Financing = 'Financing',
    B2B = 'B2B',
    RecruitmentDetail = 'RecruitmentDetail',
    AdministrativeLicense = 'AdministrativeLicense',
    SimilarCom = 'SimilarCom',
    TradeShow = 'TradeShow',
    PromotionDetail = 'PromotionDetail',
    ImportAndExportCredit = 'ImportAndExportCredit',
    TaxQualify = 'TaxQualify',
    PromotionOverview = 'PromotionOverview',
    Tender = 'Tender',
    LawOfficeInfo = 'LawOfficeInfo',
    TaxCredit = 'TaxCredit',
    RegisterPerson = 'RegisterPerson',
    RecruitmentOverview = 'RecruitmentOverview',
    ShopInfo = 'ShopInfo',
    EcomOverview = 'EcomOverview',
    BrandInfo = 'BrandInfo',
    BrandInfoOverView = 'BrandInfoOverView',
    Wechat = 'Wechat',
    Microblog = 'Microblog',
    APPViewAndDetail = 'APPViewAndDetail',
    StandardInfoOverView = 'StandardInfoOverView',
    Patents = 'Patents',
    WebsiteInformation = 'WebsiteInformation',
    softWareCopyright = 'softWareCopyright',
    opusCopyright = 'opusCopyright',
    TradeMark = 'TradeMark',
    PatentsOverview = 'PatentsOverview',
    Certificate = 'Certificate',
    IllegalInfoCB = 'IllegalInfoCB',
    TaxArrearsNoticeDetail = 'TaxArrearsNoticeDetail',
    Abnormality = 'Abnormality',
    AdministrativePenalty = 'AdministrativePenalty',
    AdministrativePenaltyTax = 'AdministrativePenaltyTax',
    Dishonest = 'Dishonest',
    EndBookInfo = 'EndBookInfo',
    CourtAnnouncement = 'CourtAnnouncement',
    HeightLimitInfoCB = 'HeightLimitInfoCB',
    Executor = 'Executor',
    JudicialAssist = 'JudicialAssist',
    EquityPledged = 'EquityPledged',
    AnnTrialInfo = 'AnnTrialInfo',
    TaxAbnormal = 'TaxAbnormal',
    JudgementsInfo = 'JudgementsInfo',
    ChattelMortgageInfoCB = 'ChattelMortgageInfoCB',
    TaxIllegal = 'TaxIllegal',
    SpotCheckInfo = 'SpotCheckInfo',
}

// 定义页面配置项的接口
export interface PageConfigItem {
    title: string
    key: string
    component: 'Info' | 'List'
    columns?: Column[]
}

export type PageConfigType = {
    [key in RequestKeys]: PageConfigItem
}

export const PageConfig: PageConfigType = {
    [RequestKeys.GSInfo]: {
        title: '工商信息',
        key: RequestKeys.GSInfo,
        component: 'Info',
        columns: [
            { name: '法定代表人', key: 'legalperson', type: ColumnType.RelateCompanyLink },

            { name: '注册资本', key: 'regCapDisplay', type: ColumnType.Text, groupIndex: 1 },
            { name: '工商注册号', key: 'regno', type: ColumnType.Text, groupIndex: 1 },

            { name: '组织机构代码', key: 'oc', type: ColumnType.Text, groupIndex: 2 },

            // { name: '实缴资本', key: 'regccap', type: ColumnType.Text },
            { name: '公司类型', key: 'enttype', type: ColumnType.Text, groupIndex: 2 },
            // { name: '所属行业', key: 'industry', type: ColumnType.Text },

            { name: '统一社会信用代码', key: 'uncid', type: ColumnType.Text },
            { name: '核准日期', key: 'apprdate', type: ColumnType.Text, groupIndex: 3 },
            {
                name: '营业期限',
                key: 'opfrom',
                render: (rowData) => rowData.opfrom + '至' + rowData.opto,
                type: ColumnType.Text,
                groupIndex: 3,
            },

            { name: '英文名', key: 'entNameEng', type: ColumnType.Text },
            { name: '注册地址', key: 'address', type: ColumnType.Text },
            { name: '网址', key: 'officialWebsite', type: ColumnType.Text },
            { name: '主营范围', key: 'businessscope', type: ColumnType.Text, textCollapse: true },
        ],
    },
    [RequestKeys.EntInfo]: {
        title: '企业简介',
        key: RequestKeys.EntInfo,
        component: 'Info',
        columns: [
            {
                name: '企业简介',
                key: 'companyInfo',
                type: ColumnType.Text,
                textCollapse: false,
            },
        ],
    },
    [RequestKeys.Shareholder]: {
        title: '股东信息',
        key: RequestKeys.Shareholder,
        component: 'List',
        columns: [
            {
                name: '持股比例',
                key: 'INSTO',
                type: ColumnType.Text,
                render: (rowData) => Number(rowData.INSTO).toFixed(2) + '%',
            },

            { name: '股东信息', key: 'INV', type: ColumnType.RelateCompanyLink },
            { name: '股东类型', key: 'INVTYPE', type: ColumnType.Text },
            { name: '实缴出资额', key: 'LIACCONAM', type: ColumnType.Text },
            { name: '认缴出资额', key: 'LISUBCONAM', type: ColumnType.Text },
        ],
    },
    [RequestKeys.Investment]: {
        title: '对外投资',
        key: RequestKeys.Investment,
        component: 'List',
        columns: [
            { name: '被投资企业', key: 'ENTNAME', type: ColumnType.CompanyProfileLink },
            { name: '成立日期', key: 'ESDATE', type: ColumnType.Text },
            {
                name: '出资比例',
                key: 'INSTO',
                type: ColumnType.Text,
                render: (rowData) => Number(rowData.INSTO).toFixed(2) + '%',
            },
            { name: '法定代表人', key: 'LEGALPERSON', type: ColumnType.RelateCompanyLink },
        ],
    },
    [RequestKeys.BaikeInfo]: {
        title: '企业百科',
        key: RequestKeys.BaikeInfo,
        component: 'List',
        columns: [
            { name: '链接', key: 'URL', type: ColumnType.Text },
            { name: '来源名称', key: 'sourceName', type: ColumnType.Text },
            { name: '内容', key: 'description', type: ColumnType.Text },
        ],
    },
    [RequestKeys.KeyPerson]: {
        title: '主要人员',
        key: RequestKeys.KeyPerson,
        component: 'List',
        columns: [
            { name: '职位', key: 'position_CN', type: ColumnType.Text },
            { name: '姓名', key: 'name', type: ColumnType.RelateCompanyLink },
        ],
    },
    [RequestKeys.AlterInfo]: {
        title: '工商变更',
        key: RequestKeys.AlterInfo,
        component: 'List',
        columns: [
            { name: '变更日期', key: 'alterDate', type: ColumnType.Text },
            { name: '变更前', key: 'alterBefore', type: ColumnType.Text },
            { name: '变更后', key: 'alterAfter', type: ColumnType.Text },
            { name: '变更事项', key: 'alterItem', type: ColumnType.Text },
        ],
    },
    [RequestKeys.Branch]: {
        title: '子公司与分支机构',
        key: RequestKeys.Branch,
        component: 'List',
        columns: [
            { name: '企业关系类型', key: 'TYPE', type: ColumnType.Text },
            { name: '企业名称', key: 'ENTNAME', type: ColumnType.Text },
            { name: '法定代表人', key: 'LEGALPERSON', type: ColumnType.Text },
            { name: '注册资本', key: 'REGCAP', type: ColumnType.Text },
            { name: '成立日期', key: 'ESDATE', type: ColumnType.Text },
        ],
    },
    [RequestKeys.AnnualReport]: {
        title: '企业年报',
        key: RequestKeys.AnnualReport,
        component: 'List',
        columns: [
            { name: '报送年度', key: 'reportYear', type: ColumnType.Text },
            { name: '通讯地址', key: 'address', type: ColumnType.Text },
            { name: '社保人数', key: 'socialSecNum', type: ColumnType.Text },
        ],
    },
    [RequestKeys.EnterpriseProductInfo]: {
        title: '产品信息',
        key: RequestKeys.EnterpriseProductInfo,
        component: 'List',
        columns: [
            { name: '产品名称', key: 'productName', type: ColumnType.Text },
            { name: '产品介绍', key: 'productIntroduce', type: ColumnType.Text },
        ],
    },
    [RequestKeys.OriginateTeam]: {
        title: '原始团队',
        key: RequestKeys.OriginateTeam,
        component: 'List',
        columns: [
            { name: '姓名', key: 'name', type: ColumnType.Text },
            { name: '职位', key: 'position', type: ColumnType.Text },
            { name: '人员介绍', key: 'introduce', type: ColumnType.Text },
        ],
    },
    [RequestKeys.Financing]: {
        title: '融资历史',
        key: RequestKeys.Financing,
        component: 'List',
        columns: [
            { name: '年份', key: 'financeDate', type: ColumnType.Text },
            { name: '轮次', key: 'round', type: ColumnType.Text },
            { name: '金额', key: 'finance', type: ColumnType.Text },
            {
                name: '融资方',
                key: 'investors',
                type: ColumnType.Text,
                render: (rowData) => rowData.investors.join('、'),
            },
        ],
    },
    [RequestKeys.B2B]: {
        title: '经营概览',
        key: RequestKeys.B2B,
        component: 'Info',
        columns: [
            { name: '经营范围', key: 'B2BInfo.bussinessScope', type: ColumnType.Text },
            { name: '主营产品', key: 'B2BInfo.bussinessProduct', type: ColumnType.Text },
            { name: '管理体系认证', key: 'B2BInfo.mgtCert', type: ColumnType.Text },
            { name: '经营模式', key: 'B2BInfo.bussinessModel', type: ColumnType.Text },
            { name: '代工模式', key: 'B2BInfo.foundryModel', type: ColumnType.Text },
            { name: '员工人数', key: 'B2BInfo.scale', type: ColumnType.Text },
            { name: '厂房面积', key: 'B2BInfo.factoryArea', type: ColumnType.Text },
            { name: '年营业额', key: 'B2BInfo.annualTurnover', type: ColumnType.Text },
            { name: '月产量', key: 'B2BInfo.monthYield', type: ColumnType.Text },
            { name: '主要市场', key: 'B2BInfo.businessMarket', type: ColumnType.Text },
            { name: '主要客户群', key: 'B2BInfo.businessClient', type: ColumnType.Text },
            { name: '经营品牌', key: 'B2BInfo.brand', type: ColumnType.Text },
            { name: '质量控制', key: 'B2BInfo.qualityControl', type: ColumnType.Text },
            { name: '年进口额', key: 'B2BInfo.annualImpVolume', type: ColumnType.Text },
            { name: '年出口额', key: 'B2BInfo.annualExpVolume', type: ColumnType.Text },
        ],
    },
    [RequestKeys.RecruitmentDetail]: {
        title: '招聘职位',
        key: RequestKeys.RecruitmentDetail,
        component: 'List',
        columns: [
            { name: '发布时间', key: 'releaseDate', type: ColumnType.Text },
            { name: '招聘职位', key: 'jobName', type: ColumnType.Text },
            { name: '月薪', key: 'salary', type: ColumnType.Text },
            { name: '所在地', key: 'location', type: ColumnType.Text },
            { name: '招聘平台', key: 'sourceName', type: ColumnType.Text },
            { name: '招聘平台链接', key: 'jobUrl', type: ColumnType.Text },
            { name: '地址', key: 'address', type: ColumnType.Text },
            { name: '招聘数量', key: 'amount', type: ColumnType.Text },
            { name: '福利', key: 'welfare', type: ColumnType.Text },
            {
                name: '职位描述',
                key: 'jobDescription',
                type: ColumnType.Text,
                textCollapse: true,
                defaultExpanded: false,
            },
            {
                name: '职位要求',
                key: 'jobRequirements',
                type: ColumnType.Text,
                textCollapse: true,
                defaultExpanded: false,
            },
            { name: '学历', key: 'education', type: ColumnType.Text },
            { name: '经验', key: 'experience', type: ColumnType.Text },
        ],
    },
    [RequestKeys.AdministrativeLicense]: {
        title: '行政许可',
        key: RequestKeys.AdministrativeLicense,
        component: 'List',
        columns: [
            { name: '许可文件名称', key: 'valName', type: ColumnType.Text },
            { name: '许可文件编号', key: 'licNo', type: ColumnType.Text },
            { name: '发证日期', key: 'publishDate', type: ColumnType.Text },
            { name: '开始时间', key: 'valFrom', type: ColumnType.Text },
            { name: '到期时间', key: 'valTo', type: ColumnType.Text },
            { name: '许可机关', key: 'licAnth', type: ColumnType.Text },
            { name: '许可内容', key: 'licItem', type: ColumnType.Text },
        ],
    },
    [RequestKeys.SimilarCom]: {
        title: '竞品友商',
        key: RequestKeys.SimilarCom,
        component: 'List',
        columns: [
            { name: '企业名称', key: 'COMPANYNAME', type: ColumnType.Text },
            { name: '产品名称', key: 'com_name', type: ColumnType.Text },
            { name: '成立时间', key: 'com_born_time', type: ColumnType.Text },
            { name: '轮次', key: 'invese_round_name', type: ColumnType.Text },
            { name: '标签', key: 'com_sub_cat_name', type: ColumnType.Text },
        ],
    },
    [RequestKeys.TradeShow]: {
        title: '参展信息',
        key: RequestKeys.TradeShow,
        component: 'List',
        columns: [
            { name: '展会名称', key: 'name', type: ColumnType.Text },
            {
                name: '开始时间',
                key: 'startDate',
                type: ColumnType.Text,
                render: (rowData) => formatDate(rowData.startDate),
            },

            {
                name: '结束时间',
                key: 'endDate',
                type: ColumnType.Text,
                render: (rowData) => formatDate(rowData.endDate),
            },
        ],
    },
    [RequestKeys.PromotionDetail]: {
        title: '网络推广',
        key: RequestKeys.PromotionDetail,
        component: 'List',
        columns: [
            { name: '推广时间', key: 'semDate', type: ColumnType.Text },
            { name: '推广链接', key: 'semUrl', type: ColumnType.Text },
            { name: '推广关键词数', key: 'keywordsNum', type: ColumnType.Text },
            { name: '推广文案总数', key: 'titleNum', type: ColumnType.Text },
            { name: '推广标题', key: 'semTitle', type: ColumnType.Text },
            { name: '推广平台', key: 'sourceName', type: ColumnType.Text },
            { name: '推广类型', key: 'sourceType', type: ColumnType.Text },
        ],
    },
    [RequestKeys.ImportAndExportCredit]: {
        title: '进出口信用',
        key: RequestKeys.ImportAndExportCredit,
        component: 'List',
        columns: [
            { name: '行政区划', key: 'adminisDivision', type: ColumnType.Text },
            { name: '年报情况', key: 'annStatusDesc', type: ColumnType.Text },
            { name: '跨境电子商务类型', key: 'businessType', type: ColumnType.Text },
            { name: '信用等级', key: 'creditLevel', type: ColumnType.Text },
            { name: '海关注册编码', key: 'custCode', type: ColumnType.Text },
            { name: '海关注销标志', key: 'custStatusDesc', type: ColumnType.Text },
            { name: '经济区划', key: 'economicDivision', type: ColumnType.Text },
            { name: '行业种类', key: 'indusCategory', type: ColumnType.Text },
            { name: '经营类别', key: 'opCategory', type: ColumnType.Text },
            { name: '注册海关', key: 'registerOrg', type: ColumnType.Text },
            {
                name: '报关有效期',
                key: 'validDateTo',
                type: ColumnType.Text,
                render: (rowData) => formatDate(rowData.validDateTo),
            },
            {
                name: '注册日期',
                key: 'registerDate',
                type: ColumnType.Text,
                render: (rowData) => formatDate(rowData.registerDate),
            },
        ],
    },
    [RequestKeys.TaxQualify]: {
        title: '纳税资质',
        key: RequestKeys.TaxQualify,
        component: 'List',
        columns: [
            { name: '纳税人资质', key: 'taxType', type: ColumnType.Text },
            { name: '纳税人识别号', key: 'UNCID', type: ColumnType.Text },
            { name: '发证时间', key: 'startDate', type: ColumnType.Text },
            { name: '到期时间', key: 'endDate', type: ColumnType.Text },
            { name: '税务机关', key: 'taxOrgan', type: ColumnType.Text },
        ],
    },
    [RequestKeys.PromotionOverview]: {
        title: '网络推广概况',
        key: RequestKeys.PromotionOverview,
        component: 'Info',
        columns: [
            { name: '最新推广时间', key: 'lastMarketDate', type: ColumnType.Text },
            { name: '推广渠道', key: 'marketChannels', type: ColumnType.Text },
            { name: '推广链接', key: 'marketUrls', type: ColumnType.Text },
        ],
    },
    [RequestKeys.Tender]: {
        title: '招标投标',
        key: RequestKeys.Tender,
        component: 'List',
        columns: [
            { name: '发布时间', key: 'publicDate', type: ColumnType.Text },
            { name: '项目编号', key: 'ID', type: ColumnType.Text },
            { name: '项目描述', key: 'projectName', type: ColumnType.Text },
            { name: '所在地区', key: 'region', type: ColumnType.Text },
            { name: '类型', key: 'tenderType', type: ColumnType.Text },
            // { name: '项目描述链接', key: 'URL', type: ColumnType.Text },
        ],
    },
    [RequestKeys.LawOfficeInfo]: {
        title: '律师信息',
        key: RequestKeys.LawOfficeInfo,
        component: 'List',
        columns: [
            { name: '头像', key: 'logoUrl', type: ColumnType.Text },
            { name: '姓名', key: 'lawer', type: ColumnType.Text },
            { name: '注册号', key: 'certId', type: ColumnType.Text },
            { name: '工龄', key: 'workAge', type: ColumnType.Text },
        ],
    },
    [RequestKeys.TaxCredit]: {
        title: '纳税信用',
        key: RequestKeys.TaxCredit,
        component: 'List',
        columns: [
            { name: '评价年度', key: 'year', type: ColumnType.Text },
            { name: '纳税信用等级', key: 'creditLevel', type: ColumnType.Text },
            { name: '纳税人识别号', key: 'identifyNumber', type: ColumnType.Text },
        ],
    },
    [RequestKeys.RegisterPerson]: {
        title: '注册人员',
        key: RequestKeys.RegisterPerson,
        component: 'List',
        columns: [
            { name: '姓名', key: 'register', type: ColumnType.Text },
            { name: '注册类别', key: 'certType', type: ColumnType.Text },
            { name: '注册号', key: 'registerNo', type: ColumnType.Text },
            { name: '注册专业', key: 'specialty', type: ColumnType.Text },
        ],
    },
    [RequestKeys.RecruitmentOverview]: {
        title: '招聘概览',
        key: RequestKeys.RecruitmentOverview,
        component: 'Info',
        columns: [
            { name: '招聘渠道', key: 'channelResult.num', type: ColumnType.Text },
            { name: '招聘地址', key: 'cityResult.item', type: ColumnType.Text },
            { name: '当前招聘职位数', key: 'posCount', type: ColumnType.Text },
            { name: '前3个月招聘职位数', key: 'posCountNear', type: ColumnType.Text },
            { name: '职位平均更新频率', key: 'avgFrequency', type: ColumnType.Text },
            { name: '企业平均薪资', key: 'avgSalary', type: ColumnType.Text },
            { name: '招聘类型', key: 'recruitCategory', type: ColumnType.Text },
            { name: '企业福利', key: 'welfare', type: ColumnType.Text },
            { name: '职位业务关键词', key: 'businessKeyword', type: ColumnType.Text },
            { name: '职位职级关键词', key: 'jobGradeKeyword', type: ColumnType.Text },
        ],
    },
    [RequestKeys.ShopInfo]: {
        title: '电商数据',
        key: RequestKeys.ShopInfo,
        component: 'List',
        columns: [
            { name: '店铺名称', key: 'name', type: ColumnType.Text },
            { name: '上架平台', key: 'platform', type: ColumnType.Text },
        ],
    },
    [RequestKeys.EcomOverview]: {
        title: '电商数据概览',
        key: RequestKeys.EcomOverview,
        component: 'Info',
        columns: [
            { name: '电商店铺平均评分', key: 'avgScore', type: ColumnType.Text },
            { name: '产品分类', key: 'productCategoryCount', type: ColumnType.Text },
            { name: '店铺商品总数', key: 'productCount', type: ColumnType.Text },
            { name: '店铺创建时间', key: 'esDate', type: ColumnType.Text },
            { name: '主营品牌', key: 'mainBrandCount', type: ColumnType.Text },
            { name: '上架平台', key: 'platform', type: ColumnType.Text },
        ],
    },
    [RequestKeys.BrandInfo]: {
        title: '品牌数据',
        key: RequestKeys.BrandInfo,
        component: 'List',
        columns: [
            { name: '品牌名称', key: 'brandName', type: ColumnType.Text },
            { name: '成立时间', key: 'brandEsDate', type: ColumnType.Text },
            { name: '所在地', key: 'brandFrom', type: ColumnType.Text },
            { name: '简介', key: 'brandInfo', type: ColumnType.Text, textCollapse: true, defaultExpanded: false },
        ],
    },
    [RequestKeys.BrandInfoOverView]: {
        title: '品牌数据概览',
        key: RequestKeys.BrandInfoOverView,
        component: 'List',
        columns: [
            { name: ' 主营产品', key: 'brandProduct', type: ColumnType.Text },
            { name: '品牌所属地', key: 'brandFrom', type: ColumnType.Text },
            { name: '品牌创立年份', key: 'brandYear', type: ColumnType.Text },
            { name: '品牌所属行业', key: 'brandIndustry', type: ColumnType.Text },
        ],
    },
    [RequestKeys.Wechat]: {
        title: '微信公众号',
        key: RequestKeys.Wechat,
        component: 'List',
        columns: [
            { name: '微信公众号名', key: 'wechatName', type: ColumnType.Text },
            { name: '微信账号', key: 'wechatAccount', type: ColumnType.Text },
        ],
    },
    [RequestKeys.Microblog]: {
        title: '机构微博',
        key: RequestKeys.Microblog,
        component: 'List',
        columns: [
            { name: '微博名称', key: 'blogNickName', type: ColumnType.Text },
            { name: '所在地区', key: 'region', type: ColumnType.Text },
            { name: '关注数量', key: 'attentionNum', type: ColumnType.Text },
            { name: '粉丝数量', key: 'fansNum', type: ColumnType.Text },
            { name: '微博数', key: 'blogNum', type: ColumnType.Text },
            { name: '标签', key: 'tags', type: ColumnType.Text },
            { name: '简介', key: 'blogInfo', type: ColumnType.Text },
        ],
    },
    [RequestKeys.APPViewAndDetail]: {
        title: '移动应用',
        key: RequestKeys.APPViewAndDetail,
        component: 'List',
        columns: [
            { name: '应用', key: 'appName', type: ColumnType.Text },
            { name: '平台', key: 'sourceName', type: ColumnType.Text },
            { name: '分类', key: 'appCategory', type: ColumnType.Text },
            { name: '发布时间', key: 'appReleaseDate', type: ColumnType.Text },
            { name: '最近更新时间', key: 'appUpdateDate', type: ColumnType.Text },
            { name: '应用描述', key: 'appDescribe', type: ColumnType.Text, textCollapse: true, defaultExpanded: false },
            { name: '相关推荐', key: 'recommendCount', type: ColumnType.Text },
            { name: '系统', key: 'appOS', type: ColumnType.Text },
        ],
    },
    [RequestKeys.StandardInfoOverView]: {
        title: '标准信息',
        key: RequestKeys.StandardInfoOverView,
        component: 'List',
        columns: [
            { name: '标准号', key: 'standardNumber', type: ColumnType.Text },
            { name: '标准名称', key: 'standardName', type: ColumnType.Text },
            { name: '标准级别', key: 'standardRank', type: ColumnType.Text },
            { name: '标准性质', key: 'standardProperty', type: ColumnType.Text },
            { name: '发布日期', key: 'releaseDate', type: ColumnType.Text },
            { name: '标准状态', key: 'standardState', type: ColumnType.Text },
        ],
    },
    [RequestKeys.Patents]: {
        title: '专利列表',
        key: RequestKeys.Patents,
        component: 'List',
        columns: [
            { name: '专利名称', key: 'PATENTNAME', type: ColumnType.Text },
            { name: '专利类型', key: 'NOTICETYPE', type: ColumnType.Text },
            { name: '申请号', key: 'APPLYPUBNO', type: ColumnType.Text },
            { name: '公开日期', key: 'APPLYPUBDATE', type: ColumnType.Text },
            { name: '公开号', key: 'APPLYNO', type: ColumnType.Text },
            { name: '申请时间', key: 'APPLYDATE', type: ColumnType.Text },
        ],
    },
    [RequestKeys.WebsiteInformation]: {
        title: '网站信息',
        key: RequestKeys.WebsiteInformation,
        component: 'List',
        columns: [
            { name: '网站名称', key: 'WEBNAME', type: ColumnType.Text },
            { name: '网址', key: 'SITEHOME', type: ColumnType.Text },
            { name: '网站名称', key: 'SITENAME', type: ColumnType.Text },
            { name: '网站备案/许可证号', key: 'RECORDID', type: ColumnType.Text },
        ],
    },
    [RequestKeys.softWareCopyright]: {
        title: '软件著作权',
        key: RequestKeys.softWareCopyright,
        component: 'List',
        columns: [
            { name: '软件名称', key: 'softwareName', type: ColumnType.Text },
            { name: '软件简称', key: 'shortName', type: ColumnType.Text },
            { name: '版本号', key: 'version', type: ColumnType.Text },
            { name: '发布日期', key: 'releaseDate', type: ColumnType.Text },
            { name: '登记号', key: 'registNo', type: ColumnType.Text },
            { name: '登记批准日期', key: 'registDate', type: ColumnType.Text },
        ],
    },
    [RequestKeys.opusCopyright]: {
        title: '作品著作权',
        key: RequestKeys.opusCopyright,
        component: 'List',
        columns: [
            { name: '作品名称', key: 'worksName', type: ColumnType.Text },
            { name: '创作完成日期', key: 'releaseDate', type: ColumnType.Text },
            { name: '登记号', key: 'registNo', type: ColumnType.Text },
            { name: '登记日期', key: 'registDate', type: ColumnType.Text },
            { name: '登记类型', key: 'registCategory', type: ColumnType.Text },
        ],
    },
    [RequestKeys.TradeMark]: {
        title: '商标信息',
        key: RequestKeys.TradeMark,
        component: 'List',
        columns: [
            { name: '商标', key: 'MARKIMGOSS', type: ColumnType.Text },
            { name: '商标名称', key: 'MARKNAME', type: ColumnType.Text },
            { name: '注册号', key: 'APPLYNO', type: ColumnType.Text },
            { name: '申请时间', key: 'APPLYDATE', type: ColumnType.Text },
            { name: '商标类型编号', key: 'markTypeStr', type: ColumnType.Text },
            { name: '状态', key: 'STATUS', type: ColumnType.Text },
            { name: '注册号', key: 'REGISTERNO', type: ColumnType.Text },
            { name: '专用权结束时间', key: 'USERIGHTDATEEND', type: ColumnType.Text },
        ],
    },
    [RequestKeys.PatentsOverview]: {
        title: '专利概况',
        key: RequestKeys.PatentsOverview,
        component: 'Info',
        columns: [
            { name: '发明公布专利数', key: 'inventionPublishedCount', type: ColumnType.Text },
            { name: '发明授权专利数', key: 'inventionotherAuthorizedCount', type: ColumnType.Text },
            { name: '实用新型专利数', key: 'utilityModelCount', type: ColumnType.Text },
            { name: '外观设计专利数', key: 'appearanceDesignCount', type: ColumnType.Text },
            { name: '最近1年申请专利数', key: 'nearYearAppliedCount', type: ColumnType.Text },
            { name: '最近1年公开(公告)专利数', key: 'nearYearPublishedCount', type: ColumnType.Text },
        ],
    },
    [RequestKeys.Certificate]: {
        title: '资质证书',
        key: RequestKeys.Certificate,
        component: 'List',
        columns: [
            { name: '证书类型', key: 'categoryL1', type: ColumnType.Text },
            { name: '证书编号', key: 'certId', type: ColumnType.Text },
            { name: '证书名称', key: 'certName', type: ColumnType.Text },
            { name: '发证机构', key: 'organName', type: ColumnType.Text },
            { name: '证书状态', key: 'status', type: ColumnType.Text },
            { name: '发证日期', key: 'startDate', type: ColumnType.Text },
            { name: '截止日期', key: 'endDate', type: ColumnType.Text },
        ],
    },
    [RequestKeys.IllegalInfoCB]: {
        title: '严重违法',
        key: RequestKeys.IllegalInfoCB,
        component: 'List',
        columns: [
            { name: '类型', key: 'category', type: ColumnType.Text },
            { name: '列入日期', key: 'dateInclusion', type: ColumnType.Text },
            { name: '列入原因', key: 'reasonInclusion', type: ColumnType.Text },
            { name: '列入机关', key: 'decisionInstitution', type: ColumnType.Text },
            { name: '移出日期', key: 'dateRemoval', type: ColumnType.Text },
            { name: '移出原因', key: 'reasonRemoval', type: ColumnType.Text },
            { name: '移出机关', key: 'decisionInstitutionRemove', type: ColumnType.Text },
        ],
    },
    [RequestKeys.TaxArrearsNoticeDetail]: {
        title: '欠税公告',
        key: RequestKeys.TaxArrearsNoticeDetail,
        component: 'List',
        columns: [
            { name: '欠税类型', key: 'type', type: ColumnType.Text },
            { name: '公示日期', key: 'bulletinTime', type: ColumnType.Text },
            { name: '历史欠税金额', key: 'arrearsHistory', type: ColumnType.Text },
            { name: '本年度欠税金额', key: 'arrears', type: ColumnType.Text },
            { name: '税务机关', key: 'organName', type: ColumnType.Text },
            { name: '合计欠税金额', key: 'total', type: ColumnType.Text },
        ],
    },
    [RequestKeys.Abnormality]: {
        title: '经营异常',
        key: RequestKeys.Abnormality,
        component: 'List',
        columns: [
            { name: '列入日期', key: 'dateInclusion', type: ColumnType.Text },
            { name: '列入经营异常原因', key: 'reasonInclusion', type: ColumnType.Text },
            { name: '决定机构', key: 'decisionInstitution', type: ColumnType.Text },
            { name: '移出日期', key: 'dateRemoval', type: ColumnType.Text },
            { name: '移出经营异常原因', key: 'reasonRemoval', type: ColumnType.Text },
        ],
    },
    [RequestKeys.AdministrativePenalty]: {
        title: '行政处罚',
        key: RequestKeys.AdministrativePenalty,
        component: 'List',
        columns: [
            { name: '决定文书号', key: 'penDecNo', type: ColumnType.Text },
            { name: '公司名称', key: 'COMPANYNAME', type: ColumnType.Text },
            { name: '处罚类型', key: 'penType', type: ColumnType.Text },
            { name: '处罚依据', key: 'penBase', type: ColumnType.Text },
            { name: '处罚事由', key: 'penReason', type: ColumnType.Text },
            { name: '处罚机关', key: 'penotherAuth', type: ColumnType.Text },
            { name: '处罚结果', key: 'penContent', type: ColumnType.Text },
            { name: '处罚决定日期', key: 'penDate', type: ColumnType.Text },
        ],
    },
    [RequestKeys.AdministrativePenaltyTax]: {
        title: '税务行政处罚',
        key: RequestKeys.AdministrativePenaltyTax,
        component: 'List',
        columns: [
            { name: '处罚机关', key: 'penotherAuth', type: ColumnType.Text },
            { name: '处罚依据', key: 'penBase', type: ColumnType.Text },
            { name: '处罚结果', key: 'penContent', type: ColumnType.Text },
            { name: '处罚类型', key: 'penType', type: ColumnType.Text },
            { name: '处罚决定日期', key: 'penDate', type: ColumnType.Text },
            { name: '处罚事由', key: 'penReason', type: ColumnType.Text },
            { name: '决定文书号', key: 'penDecNo', type: ColumnType.Text },
        ],
    },
    [RequestKeys.Dishonest]: {
        title: '失信被执行人',
        key: RequestKeys.Dishonest,
        component: 'List',
        columns: [
            { name: '案号', key: 'REFERENCENO', type: ColumnType.Text },
            { name: '执行法院', key: 'COURT', type: ColumnType.Text },
            { name: '失信具体行为', key: 'ENFDUTY', type: ColumnType.Text },
            { name: '履行情况', key: 'ENFSTATUS', type: ColumnType.Text },
            { name: '立案日期', key: 'FILINGDATE', type: ColumnType.Text },
            { name: '发布日期', key: 'PUBDATE', type: ColumnType.Text },
            { name: '失信行为具体情形', key: 'ENFSITUATION', type: ColumnType.Text },
        ],
    },
    [RequestKeys.EndBookInfo]: {
        title: '终本案件',
        key: RequestKeys.EndBookInfo,
        component: 'List',
        columns: [
            { name: '案号', key: 'REFERENCENO', type: ColumnType.Text },
            { name: '执行法院', key: 'COURT', type: ColumnType.Text },
            { name: '执行标的（元）', key: 'ENFOBJECT', type: ColumnType.Text },
            { name: '未履行金额', key: 'UNENFOBJECT', type: ColumnType.Text },
            { name: '立案日期', key: 'FILINGDATE', type: ColumnType.Text },
            { name: '终本日期', key: 'WEBDATE', type: ColumnType.Text },
        ],
    },
    [RequestKeys.CourtAnnouncement]: {
        title: '法院公告',
        key: RequestKeys.CourtAnnouncement,
        component: 'List',
        columns: [
            { name: '开庭地址', key: 'address', type: ColumnType.Text },
            { name: '案号', key: 'caseNum', type: ColumnType.Text },
            { name: '公告类型', key: 'noticeType', type: ColumnType.Text },
            { name: '执行法院', key: 'court', type: ColumnType.Text },
            { name: '公告内容', key: 'content', type: ColumnType.Text },
            { name: '起诉方', key: 'accuserInfo', type: ColumnType.Text },
            { name: '第三人', key: 'thirdPartyInfo', type: ColumnType.Text },
            { name: '开庭日期', key: 'courtDate', type: ColumnType.Text },
            { name: '公告日期', key: 'publishDate', type: ColumnType.Text },
        ],
    },
    [RequestKeys.HeightLimitInfoCB]: {
        title: '限制高消费',
        key: RequestKeys.HeightLimitInfoCB,
        component: 'List',
        columns: [
            { name: '限制消费人员', key: 'enfName', type: ColumnType.Text },
            { name: '立案日期', key: 'filingDate', type: ColumnType.Text },
            { name: '案号', key: 'referenceNo', type: ColumnType.Text },
        ],
    },
    [RequestKeys.Executor]: {
        title: '被执行人',
        key: RequestKeys.Executor,
        component: 'List',
        columns: [
            { name: '案号', key: 'REFERENCEMO', type: ColumnType.Text },
            { name: '执行法院', key: 'COURT', type: ColumnType.Text },
            { name: '执行标的（元）', key: 'ENFOBJECT', type: ColumnType.Text },
            { name: '立案日期', key: 'FILINGDATE', type: ColumnType.Text },
        ],
    },
    [RequestKeys.JudicialAssist]: {
        title: '司法协助',
        key: RequestKeys.JudicialAssist,
        component: 'List',
        columns: [
            { name: '被执行人', key: 'enfName', type: ColumnType.Text },
            { name: '股权数额', key: 'enfObject', type: ColumnType.Text },
            { name: '执行法院', key: 'court', type: ColumnType.Text },
            { name: '执行通知书文号', key: 'referenceNo', type: ColumnType.Text },
            { name: '执行事项', key: 'item', type: ColumnType.Text },
            { name: '状态', key: 'state', type: ColumnType.Text },
            { name: '公示日期', key: 'filingDate', type: ColumnType.Text },
        ],
    },
    [RequestKeys.EquityPledged]: {
        title: '股权出质',
        key: RequestKeys.EquityPledged,
        component: 'List',
        columns: [
            { name: '登记编号', key: 'registNo', type: ColumnType.Text },
            { name: '出质人', key: 'pledgor', type: ColumnType.Text },
            { name: '质权人', key: 'pledgee', type: ColumnType.Text },
            { name: '出质股权质额', key: 'pledgorAmount', type: ColumnType.Text },
            { name: '出质设计登记时间', key: 'registDate', type: ColumnType.Text },
            { name: '公示日期', key: 'publicDate', type: ColumnType.Text },
            { name: '状态', key: 'status', type: ColumnType.Text },
        ],
    },
    [RequestKeys.AnnTrialInfo]: {
        title: '开庭公告',
        key: RequestKeys.AnnTrialInfo,
        component: 'List',
        columns: [
            { name: '开庭日期', key: 'contentDate', type: ColumnType.Text },
            { name: '案号', key: 'caseNum', type: ColumnType.Text },
            { name: '案件类型', key: 'caseType', type: ColumnType.Text },
            { name: '案件类型排序', key: 'caseTypeDesc', type: ColumnType.Text },
            { name: '审判法院', key: 'court', type: ColumnType.Text },
            { name: '开庭地址', key: 'trialLocation', type: ColumnType.Text },
            { name: '发布日期', key: 'publishDate', type: ColumnType.Text },
            { name: '公告内容', key: 'content', type: ColumnType.Text },
            { name: '发布版本', key: 'publishedEdition', type: ColumnType.Text },
        ],
    },
    [RequestKeys.TaxAbnormal]: {
        title: '税务非正常户',
        key: RequestKeys.TaxAbnormal,
        component: 'List',
        columns: [
            { name: '主管税务机关', key: 'organName', type: ColumnType.Text },
            { name: '纳税人状态', key: 'statusDesc', type: ColumnType.Text },
            { name: '认定日期', key: 'releaseDate', type: ColumnType.Text },
        ],
    },
    [RequestKeys.JudgementsInfo]: {
        title: '裁判文书',
        key: RequestKeys.JudgementsInfo,
        component: 'List',
        columns: [
            { name: '案件名称', key: 'title', type: ColumnType.Text },
            { name: '案由类型', key: 'caseReason', type: ColumnType.Text },
            { name: '案件类型', key: 'caseType', type: ColumnType.Text },
            { name: '案号', key: 'caseNum', type: ColumnType.Text },
            { name: '审判程序', key: 'trialProcedure', type: ColumnType.Text },
            { name: '发布日期', key: 'publishDate', type: ColumnType.Text },
            { name: '执行法院', key: 'court', type: ColumnType.Text },
        ],
    },
    [RequestKeys.ChattelMortgageInfoCB]: {
        title: '动产抵押',
        key: RequestKeys.ChattelMortgageInfoCB,
        component: 'List',
        columns: [
            { name: '登记编号', key: 'registNo', type: ColumnType.Text },
            { name: '登记机关', key: 'organName', type: ColumnType.Text },
            { name: '登记日期', key: 'registDate', type: ColumnType.Text },
            { name: '被担保债券数额', key: 'guaranteeBondDesc', type: ColumnType.Text },
            { name: '状态', key: 'status', type: ColumnType.Text },
        ],
    },
    [RequestKeys.TaxIllegal]: {
        title: '重大税收违法失信',
        key: RequestKeys.TaxIllegal,
        component: 'List',
        columns: [
            { name: '主要违法事实', key: 'mainIllegalCase', type: ColumnType.Text },
            { name: '违法类型', key: 'illegalCaseKind', type: ColumnType.Text },
            { name: '处罚情况', key: 'punishment', type: ColumnType.Text },
        ],
    },
    [RequestKeys.SpotCheckInfo]: {
        title: '抽查检查',
        key: RequestKeys.SpotCheckInfo,
        component: 'List',
        columns: [
            { name: '检查类型', key: 'insType', type: ColumnType.Text },
            { name: '检查日期', key: 'insDate', type: ColumnType.Text },
            { name: '检查机关', key: 'reasonInclusion', type: ColumnType.Text },
            { name: '检查结果', key: 'insResult', type: ColumnType.Text },
        ],
    },
}
