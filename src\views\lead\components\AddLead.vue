<script lang="ts" setup>
import { ref, reactive, watch, computed } from 'vue'
import crmService from '@/service/crmService'
import { showNotify } from 'vant'
import aicService from '@/service/aicService'
import type { FormInstance } from 'vant'
import type { ICompanyInfo } from '@/types/company'

const props = defineProps<{
    from: string
}>()
const typeStr = computed(() => {
    return props.from === 'lead' ? '线索' : '客户'
})
const dialogVisible = ref(false)
type FormType = {
    leadId?: string
    companyName: string
    socialCreditCode: string
    phone?: string
    channel?: string
    note?: string
    clueType?: number
    source?: number
}
const form = reactive<FormType>({
    leadId: '',
    companyName: '',
    socialCreditCode: '',
    phone: '',
    channel: '',
    note: '',
    clueType: props.from === 'lead' ? 2 : 3,
    source: 14, // 线索来源: 新增
})

const formRef = ref<FormInstance>()
const sendLoading = ref(false)
const emit = defineEmits(['refreshData'])
const onSubmit = () => {
    console.log('formRef', formRef, formRef.value)
    if (!formRef.value) return
    formRef.value
        .validate()
        .then(() => {
            sendLoading.value = true
            crmService
                .crmAdd(form)
                .then((res) => {
                    console.log('res', res)
                    const { errCode } = res
                    if (errCode === 0) {
                        showNotify({ type: 'success', message: '新增成功' })
                        dialogVisible.value = false
                        emit('refreshData')
                    } else {
                        showNotify({ type: 'danger', message: res.errMsg || '新增失败' })
                        dialogVisible.value = false
                        emit('refreshData')
                    }
                })
                .finally(() => {
                    sendLoading.value = false
                })
        })
        .catch()
}

watch(
    () => dialogVisible.value,
    (newVal) => {
        if (!newVal) {
            form.leadId = ''
            form.companyName = ''
            form.socialCreditCode = ''
            form.phone = ''
            form.channel = ''
            form.note = ''
            form.clueType = props.from === 'lead' ? 2 : 3
            form.source = 14 // 线索来源: 新增
        }
    }
)

const customFieldName = {
    text: 'companyName',
    value: 'socialCreditCode',
}
const showPicker = ref(false)
const companySearchLoading = ref(false)
const companyList = ref<ICompanyInfo[]>([])
const socialCreditCodeIptDisabled = ref(false)
const searchCompany = (query: string) => {
    companySearchLoading.value = true
    if (query) {
        setTimeout(async () => {
            let getCompanyByCodeRes = await aicService.searchEnterprise({ keyword: query, scope: '0' })
            if (getCompanyByCodeRes.success === true) {
                companySearchLoading.value = false
                companyList.value = getCompanyByCodeRes.data
            }
        }, 200)
    } else {
        companyList.value = []
        companySearchLoading.value = false
    }
}
const searchVal = ref('')
watch(
    () => searchVal.value,
    (newVal) => {
        searchCompany(newVal)
    }
)
const onCancel = () => {
    showPicker.value = false
}
const onConfirm = ({ selectedOptions }) => {
    const { companyName, socialCreditCode } = selectedOptions[0]
    form.companyName = companyName
    form.socialCreditCode = socialCreditCode
    socialCreditCodeIptDisabled.value = true
    showPicker.value = false
}
watch(
    () => showPicker.value,
    (newVal) => {
        if (!newVal) {
            searchVal.value = ''
            companyList.value = []
        }
    }
)
</script>
<template>
    <div
        class="!color-blue font-14 tb-pading-4 lr-padding-8 border-radius-4"
        style="border: 1px solid var(--main-blue-); background-color: #e6f0ff"
        @click="dialogVisible = true"
    >
        <van-icon name="plus" />
        新增{{ typeStr }}
    </div>

    <!-- 弹框区域 -->
    <van-dialog v-model:show="dialogVisible" show-cancel-button>
        <template #title>
            <div class="font-16 color-black font-weight-400">新增{{ typeStr }}</div>
        </template>
        <van-form label-align="top" ref="formRef">
            <van-cell-group inset>
                <van-field
                    v-model="form.companyName"
                    required
                    name="companyName"
                    label="企业名称"
                    placeholder="请输入企业名称"
                    :rules="[{ required: true, message: '请输入企业名称' }]"
                    clearable
                    is-link
                    readonly
                    @click="showPicker = true"
                />
                <van-field
                    v-model="form.socialCreditCode"
                    required
                    name="socialCreditCode"
                    label="企业税号"
                    placeholder="请输入企业税号"
                    :rules="[{ required: true, message: '请输入企业税号' }]"
                    clearable
                    :disabled="socialCreditCodeIptDisabled"
                />
                <van-field
                    v-model="form.phone"
                    name="phone"
                    label="手机号"
                    placeholder="请输入手机号"
                    :rules="[{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', validateEmpty: false }]"
                    clearable
                />
                <van-field
                    v-model="form.channel"
                    name="channel"
                    label="客户渠道"
                    placeholder="请输入客户渠道"
                    clearable
                />
                <van-field v-model="form.note" name="note" label="备注" placeholder="请输入备注" clearable />
                <div class="font-12 color-two-grey lr-padding-16">
                    提示：新增时将扣除一次线索权益(每家企业仅在首次新增时扣减一次线索权益，后续重复新增不再扣减)。
                </div>
            </van-cell-group>
        </van-form>
        <template #footer>
            <div class="display-flex b-padding-24 t-padding-16 h-36" style="justify-content: space-evenly">
                <van-button
                    class="w-120"
                    plain
                    type="primary"
                    style="color: var(--main-blue-); border: 1px solid var(--main-blue-)"
                    @click="dialogVisible = false"
                    >取消</van-button
                >
                <van-button
                    class="w-120"
                    type="primary"
                    style="background-color: var(--main-blue-)"
                    @click="onSubmit"
                    :loading="sendLoading"
                    loading-type="spinner"
                    loading-text="提交中"
                    >确认新增</van-button
                >
            </div>
        </template>
    </van-dialog>

    <van-popup v-model:show="showPicker" round position="bottom">
        <van-search style="margin-top: 3px" v-model="searchVal" placeholder="请输入企业名称" />
        <van-picker
            :loading="companySearchLoading"
            :columns="companyList"
            @cancel="onCancel"
            :columns-field-names="customFieldName"
            @confirm="onConfirm"
        />
    </van-popup>
</template>
<style scoped lang="scss"></style>
