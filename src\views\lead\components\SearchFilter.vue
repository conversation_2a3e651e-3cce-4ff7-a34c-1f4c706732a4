<script lang="ts" setup>
import { ref, reactive, watch, onMounted } from 'vue'
import * as allSearchOptions from '@/js/search-options'
import type { IGetCrmLeadParams, ISearchItemType } from '@/types/lead'
import type { PickerOption, CascaderOption } from 'vant'
import Icon from '@/components/common/Icon.vue'

type CustomConfig = {
    [key: string]: Array<{
        label: string
        value: string | number
    }>
}
const props = defineProps<{
    searchOptionKey: string
    customConfig?: CustomConfig
    defaultValue?: Record<string, boolean | string | number[] | string[]>
    tabType?: string // 标签类型 用以区别是否是 我的XX
    // allowAnyLevel?: boolean // 是否允许选择任意级别
}>()

const queryParams = reactive<Record<string, boolean | string | number[] | string[]>>({})

const searchVisible = ref(false)
const pickerVisible = ref(false)
const dateChooseVisible = ref(false)
const cascaderVisible = ref(false)
const currentPickerKey = ref<keyof IGetCrmLeadParams | null>(null)
const customFieldName = {
    text: 'label',
    value: 'value',
    children: 'children',
}

/*-----------------------------单选操作start---------------------------------*/
const displayValues = reactive<Record<string, string>>({}) // 用于存储选择器的显示文本
const openSearchSelect = () => {
    searchVisible.value = !searchVisible.value
}
const openPicker = (item: ISearchItemType) => {
    currentPickerKey.value = item.key
    pickerVisible.value = true
}
const onConfirm = ({ selectedOptions }: { selectedOptions: PickerOption[] }) => {
    if (currentPickerKey.value && selectedOptions.length > 0) {
        const selectedOption = selectedOptions[0]
        // 更新查询参数
        queryParams[currentPickerKey.value] = selectedOption.value
        // 更新显示文本
        displayValues[currentPickerKey.value] = selectedOption.label
    }

    pickerVisible.value = false
}
const getFieldDisplayValue = (key: keyof IGetCrmLeadParams) => {
    return displayValues[key] || ''
}
/*-----------------------------单选操作end---------------------------------*/

/*-----------------------------日期选择操作start---------------------------------*/
const openDatePicker = (item: ISearchItemType) => {
    currentPickerKey.value = item.key
    dateChooseVisible.value = true
}
const datePickConfirm = (values: [Date, Date]) => {
    if (currentPickerKey.value) {
        const [startDate, endDate] = values

        // 转换为时间戳（毫秒）
        const startTimestamp = startDate.getTime()
        const endTimestamp = endDate.setHours(23, 59, 59, 999) // 设置到当天最后一刻

        // 格式化为要求的字符串格式 'timestamp1,timestamp2'
        queryParams[currentPickerKey.value] = `${startTimestamp},${endTimestamp}`

        // 更新显示文本
        const formatDate = (date: Date) => {
            return date.toLocaleDateString('zh-CN')
        }
        displayValues[currentPickerKey.value] = `${formatDate(startDate)} 至 ${formatDate(endDate)}`
    }

    dateChooseVisible.value = false
}
const getDateDisplayValue = (key: keyof IGetCrmLeadParams) => {
    const value = queryParams[key] as string
    if (!value) return ''

    try {
        const [startTimestamp, endTimestamp] = value.split(',').map(Number)
        const startDate = new Date(startTimestamp)
        const endDate = new Date(endTimestamp)

        return `${startDate.toLocaleDateString('zh-CN')} 至 ${endDate.toLocaleDateString('zh-CN')}`
    } catch {
        return '日期格式错误'
    }
}
/*-----------------------------日期选择操作end---------------------------------*/

/*-----------------------------多选操作start---------------------------------*/
// 添加多选显示值处理
const multiSelectVisible = ref(false)
const getMultipleSelectDisplayValue = (key: keyof IGetCrmLeadParams) => {
    // 更安全的类型检查
    const selectedValues = queryParams[key]
    if (!Array.isArray(selectedValues) || selectedValues.length === 0) return ''

    const item = searchOptions.value.find((opt) => opt.key === key)
    if (!item || !item.options) return ''

    // 根据选项值的实际类型进行处理
    return selectedValues
        .map((val: string | number | boolean) => {
            const option = item.options.find((opt) => {
                // 处理不同类型的值比较
                if (typeof opt.value === 'number' && typeof val === 'string') {
                    return opt.value === Number(val)
                } else if (typeof opt.value === 'string' && typeof val === 'number') {
                    return opt.value === String(val)
                }
                return opt.value === val
            })
            return option ? option.label : ''
        })
        .filter(Boolean)
        .join('、')
}

const clearMultiSelection = () => {
    if (currentPickerKey.value) {
        // 清空查询参数（设置为空数组）
        queryParams[currentPickerKey.value] = []
    }
    // 保持弹出层打开，让用户看到清空效果
    multiSelectVisible.value = false
}
/*-----------------------------多选操作end---------------------------------*/

/*-----------------------------级联选择操作start---------------------------------*/
// 存储级联选择的当前路径
const currentCascaderPath = ref<CascaderOption[]>([])
// 打开级联选择器
const openCascader = (item: ISearchItemType) => {
    currentPickerKey.value = item.key
    currentCascaderPath.value = [] // 重置路径
    cascaderVisible.value = true
}
// 级联选择项点击
const onCascaderClick = (option: CascaderOption, selectedOptions: CascaderOption[]) => {
    // if (props.allowAnyLevel) {
    //     // 允许选择任意级别，立即确认选择
    //     cascaderConfirm({ selectedOptions: [...selectedOptions, option] })
    // }else {
    // 如果有子选项，继续选择下一级
    if (option.children && option.children.length > 0) {
        currentCascaderPath.value = [...selectedOptions, option]
    } else {
        // 没有子选项，直接确认选择
        cascaderConfirm({ selectedOptions: [...selectedOptions, option] })
    }
    // }
}
// 级联选择确认
const cascaderConfirm = ({ selectedOptions }: { selectedOptions: CascaderOption[] }) => {
    if (currentPickerKey.value && selectedOptions.length > 0) {
        // 获取最后一个选中项
        const lastSelectedOption = selectedOptions[selectedOptions.length - 1]
        // 更新查询参数
        queryParams[currentPickerKey.value] = lastSelectedOption.value
        // 更新显示文本
        const selectedTexts = selectedOptions.map((option) => option.label).join('/')
        displayValues[currentPickerKey.value] = selectedTexts
    }
    cascaderVisible.value = false
    currentCascaderPath.value = []
}

// 自定义完成按钮点击
const onCustomFinish = () => {
    if (currentCascaderPath.value.length > 0) {
        cascaderConfirm({ selectedOptions: currentCascaderPath.value })
    } else {
        cascaderVisible.value = false
    }
}

// 获取级联选择器的显示值
const getCascaderDisplayValue = (key: keyof IGetCrmLeadParams) => {
    return displayValues[key] || ''
}

// 获取当前级联选项 - 修复版本
const getCurrentCascaderOptions = () => {
    if (!currentPickerKey.value) return []
    const item = searchOptions.value.find((item) => item.key === currentPickerKey.value)
    if (!item || !item.options) return []
    let currentOptions: CascaderOption[] = item.options as CascaderOption[]
    // 根据当前路径找到对应的子选项
    for (const selectedOption of currentCascaderPath.value) {
        const nextOption = currentOptions.find((opt) => opt.value === selectedOption.value)
        if (nextOption && nextOption.children && nextOption.children.length > 0) {
            currentOptions = nextOption.children
        } else {
            break
        }
    }
    return currentOptions
}

// 添加一个方法来检查选项是否有子项
const hasChildren = (option: CascaderOption) => {
    return option.children && option.children.length > 0
}

// 清空级联选择
const clearCascaderSelection = () => {
    if (currentPickerKey.value) {
        // 清空查询参数
        queryParams[currentPickerKey.value] = undefined
        // 清空显示文本
        displayValues[currentPickerKey.value] = ''
        // 重置当前路径
        currentCascaderPath.value = []
    }
    // 关闭弹出层
    cascaderVisible.value = false
}
/*-----------------------------级联选择操作end---------------------------------*/

/*-----------------------------滑动选择操作start---------------------------------*/
const sliderValue = ref([0, 100])
const onSliderChange = (key: keyof IGetCrmLeadParams) => {
    if (sliderValue.value[0] === 0 && sliderValue.value[1] === 100) {
        delete queryParams[key]
    } else {
        queryParams[key] = sliderValue.value
    }
}
/*-----------------------------滑动选择操作end---------------------------------*/

// 重置搜索条件
const resetSearch = () => {
    Object.keys(queryParams).forEach((key) => {
        if (key !== 'page' && key !== 'pageSize') {
            queryParams[key as keyof IGetCrmLeadParams] = undefined
        }
    })
    Object.keys(displayValues).forEach((key) => {
        displayValues[key] = ''
    })
    sliderValue.value = [0, 100] //注: 目前支持只有唯一的一个滑块配置项
}

// 确认搜索
const emit = defineEmits(['updateSearchParams'])
const confirmSearch = () => {
    searchVisible.value = false
    let filterQueryParams = filterEmptyParams(queryParams)
    console.log('filterQueryParams', filterQueryParams)
    emit('updateSearchParams', filterQueryParams)
}

// 过滤空属性值
const filterEmptyParams = (queryParams: Record<string, boolean | string | number[] | string[]>) => {
    const result: Record<string, boolean | string | number[] | string[]> = {}
    for (const key in queryParams) {
        // console.log('queryParams[key]', key)
        const value = queryParams[key]
        if (
            value !== '' &&
            value !== null &&
            value !== undefined &&
            !(typeof value === 'object' && Object.keys(value).length === 0) && // 判断空对象
            JSON.stringify(value) !== JSON.stringify([0, 100])
        ) {
            result[key] = value
        }
        // console.log('result', result)
    }
    return result
}

// 初始化显示值
const searchOptions = ref<ISearchItemType[]>([])
const init = () => {
    // 线索列表/客户列表  我的tab页下去掉【负责人】
    if (
        props.tabType === 'mine' &&
        (props.searchOptionKey === 'LEAD_SEARCH_OPTIONS' || props.searchOptionKey === 'CUSTOMER_SEARCH_OPTIONS')
    ) {
        searchOptions.value = searchOptions.value.filter((item) => item.key !== 'user')
    }
    // 线索列表/客户列表 在全部tab页下增加【所属组织】
    if (
        props.tabType !== 'all' &&
        (props.searchOptionKey === 'LEAD_SEARCH_OPTIONS' || props.searchOptionKey === 'CUSTOMER_SEARCH_OPTIONS')
    ) {
        searchOptions.value = searchOptions.value.filter((item) => item.key !== 'orgIds')
    }

    async function fetchData() {
        if (props.customConfig) {
            const customConfigKeys = Object.keys(props.customConfig)
            customConfigKeys.forEach((key) => {
                const aa = searchOptions.value.find((item) => item.key === key)
                if (aa) aa.options = props.customConfig?.[key] // 使用 ?.[] 安全访问
            })
        }
    }
    fetchData()
}
watch(
    () => props.searchOptionKey as keyof typeof allSearchOptions,
    (newVal) => {
        if (newVal) {
            searchOptions.value = allSearchOptions[newVal]
        }
    },
    {
        immediate: true,
    }
)
watch(
    () => props.customConfig,
    (newVal) => {
        console.log('props.customConfig', newVal)
        init()
    }
)
watch(
    () => searchOptions.value,
    (newVal) => {
        if (newVal) {
            // 初始化日期显示值
            newVal.forEach((item) => {
                if (item.type === 'date' && queryParams[item.key]) {
                    const value = (displayValues[item.key] = getDateDisplayValue(item.key))
                    if (value) {
                        displayValues[item.key] = value
                    }
                }
            })
        }
    }
)
onMounted(() => {
    init()
})
</script>

<template>
    <div class="font-14 color-black w-50 display-flex top-bottom-center" @click="openSearchSelect">
        <span>筛选</span>
        <Icon icon="icon-shaixuan" :size="16" color="var(--main-black)" />
    </div>
    <div v-if="searchVisible">
        <van-popup
            v-model:show="searchVisible"
            position="bottom"
            safe-area-inset-top
            :style="{ height: 'auto', padding: '16px', maxHeight: '80vh', overflow: 'auto' }"
            round
        >
            <div class="search-header">
                <van-button size="small" @click="resetSearch">重置</van-button>
                <span class="search-title">筛选条件</span>
                <van-button type="primary" size="small" @click="confirmSearch">确认</van-button>
            </div>

            <div v-for="item in searchOptions" :key="item.key" class="search-item">
                <!-- 输入框类型 -->
                <div v-if="item.type === 'input'">
                    <van-field
                        v-model="queryParams[item.key]"
                        :label="item.label"
                        :placeholder="item.placeholder"
                        label-align="top"
                        clearable
                    />
                </div>

                <!-- 选择器类型 -->
                <div v-if="item.type === 'select'">
                    <van-field
                        :model-value="getFieldDisplayValue(item.key)"
                        is-link
                        readonly
                        :label="item.label"
                        :placeholder="item.placeholder"
                        @click="openPicker(item)"
                    />
                </div>

                <!-- 复选框组 -->
                <div v-if="item.type === 'multipleSelect'">
                    <van-field
                        :model-value="getMultipleSelectDisplayValue(item.key)"
                        is-link
                        readonly
                        :label="item.label"
                        :placeholder="item.placeholder"
                        @click="
                            currentPickerKey = item.key,
                            multiSelectVisible = true
                        "
                    />
                </div>

                <!-- 日期选择器类型 -->
                <div v-if="item.type === 'date'">
                    <van-field
                        :model-value="getDateDisplayValue(item.key)"
                        is-link
                        readonly
                        :label="item.label"
                        :placeholder="item.placeholder || '请选择日期区间'"
                        @click="openDatePicker(item)"
                    />
                </div>

                <!-- 级联选择器 -->
                <div v-if="item.type === 'cascader'">
                    <van-field
                        :model-value="getCascaderDisplayValue(item.key)"
                        is-link
                        readonly
                        :label="item.label"
                        :placeholder="item.placeholder"
                        @click="openCascader(item)"
                    />
                </div>

                <!-- 滑块 -->
                <div v-if="item.type === 'slider'">
                    <van-field readonly :label="item.label" label-align="top">
                        <template #input>
                            <van-slider
                                v-model="sliderValue"
                                range
                                :min="0"
                                :max="100"
                                @change="onSliderChange(item.key)"
                            >
                                <template #left-button>
                                    <div class="custom-slider-button">
                                        {{ sliderValue[0] }}
                                    </div>
                                </template>
                                <template #right-button>
                                    <div class="custom-slider-button">
                                        {{ sliderValue[1] }}
                                    </div>
                                </template>
                            </van-slider>
                        </template>
                    </van-field>
                </div>
            </div>

            <!-- 统一的 Picker 弹出层 -->
            <van-popup v-model:show="pickerVisible" destroy-on-close round position="bottom">
                <van-picker
                    :columns="searchOptions.find((item) => item.key === currentPickerKey)?.options || []"
                    :columns-field-names="customFieldName"
                    @cancel="pickerVisible = false"
                    @confirm="onConfirm"
                />
            </van-popup>

            <!-- 日期选择器弹出层 -->
            <van-popup
                v-model:show="dateChooseVisible"
                destroy-on-close
                round
                position="bottom"
                :style="{ height: '80%' }"
            >
                <van-calendar
                    v-model:show="dateChooseVisible"
                    type="range"
                    @confirm="datePickConfirm"
                    @cancel="dateChooseVisible = false"
                    :show-confirm="true"
                    switch-mode="year-month"
                />
            </van-popup>

            <!-- 级联选择器弹出层 - 自定义实现 -->
            <van-popup
                v-model:show="cascaderVisible"
                destroy-on-close
                round
                position="bottom"
                :style="{ height: '50%' }"
            >
                <div class="custom-cascader">
                    <div class="cascader-header">
                        <van-button size="small" @click="clearCascaderSelection">清空</van-button>
                        <span class="cascader-title">{{
                            searchOptions.find((item) => item.key === currentPickerKey)?.label || '请选择'
                        }}</span>
                        <van-button type="primary" size="small" @click="onCustomFinish">完成</van-button>
                    </div>

                    <div class="cascader-path" v-if="currentCascaderPath.length > 0">
                        <span
                            v-for="(option, index) in currentCascaderPath"
                            :key="index"
                            class="path-item"
                            @click="currentCascaderPath = currentCascaderPath.slice(0, index)"
                        >
                            {{ option.label }}
                            <span v-if="index < currentCascaderPath.length - 1"> > </span>
                        </span>
                    </div>

                    <div class="cascader-options">
                        <div
                            v-for="option in getCurrentCascaderOptions()"
                            :key="option.value"
                            class="cascader-option"
                            :class="{ 'has-children': hasChildren(option) }"
                            @click="onCascaderClick(option, currentCascaderPath)"
                        >
                            <span>{{ option.label }}</span>
                            <van-icon v-if="hasChildren(option)" name="arrow" />
                        </div>

                        <!-- 添加空状态提示 -->
                        <div v-if="getCurrentCascaderOptions().length === 0" class="cascader-empty">暂无选项</div>
                    </div>
                </div>
            </van-popup>

            <!-- 多选弹出层 -->
            <van-popup
                v-model:show="multiSelectVisible"
                destroy-on-close
                round
                position="bottom"
                :style="{ height: '60%' }"
            >
                <div class="multi-select-popup">
                    <div class="multi-select-header">
                        <van-button size="small" @click="clearMultiSelection">清空</van-button>
                        <!-- <div></div> -->
                        <span class="multi-select-title">
                            {{ searchOptions.find((item) => item.key === currentPickerKey)?.label || '请选择' }}
                        </span>
                        <!-- <van-icon name="cross" /> -->
                        <van-button type="primary" size="small" @click="multiSelectVisible = false">确认</van-button>
                    </div>

                    <van-checkbox-group v-if="currentPickerKey" v-model="queryParams[currentPickerKey]">
                        <van-cell-group inset>
                            <van-cell
                                v-for="option in searchOptions.find((item) => item.key === currentPickerKey)?.options || []"
                                :key="option.value"
                                clickable
                                :title="option.label"
                            >
                                <template #right-icon>
                                    <van-checkbox :name="option.value" ref="checkboxRef" />
                                </template>
                            </van-cell>
                        </van-cell-group>
                    </van-checkbox-group>
                </div>
            </van-popup>
        </van-popup>
    </div>
</template>

<style scoped lang="scss">
.search-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #eee;
}

.search-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.search-item {
    margin-bottom: 16px;
}

.color-black {
    color: #000;
}
/* 老版本的多选样式 */
// .checkbox-swipe {
//     height: 40px;

//     :deep(.van-swipe-item) {
//         display: flex;
//         align-items: center;
//     }
// }

// .chunk-checkbox-group {
//     display: flex;
//     justify-content: space-around;
//     width: 100%;
//     gap: 12px;
// }

// .chunk-checkbox {
//     flex: 1;
//     min-width: 0;

//     :deep(.van-checkbox__label) {
//         font-size: 12px;
//         white-space: nowrap;
//         overflow: hidden;
//         text-overflow: ellipsis;
//     }
// }

// 自定义级联选择器样式
.custom-cascader {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.cascader-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #eee;
}

.cascader-title {
    font-size: 16px;
    font-weight: 600;
}

.cascader-path {
    font-size: 20px;
    font-weight: 700;
    color: #3c74eb;
    padding: 12px 16px;
    border-bottom: 1px solid #f5f5f5;
    background-color: #fafafa;

    // .path-item {
    //     color: #1989fa;
    //     cursor: pointer;

    //     &:last-child {
    //         color: #333;
    //         cursor: default;
    //     }
    // }
}

.cascader-options {
    flex: 1;
    overflow-y: auto;
}

.cascader-option {
    font-size: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #f5f5f5;
    cursor: pointer;

    &:active {
        background-color: #f5f5f5;
    }

    // 为有子选项的项添加特殊样式
    &.has-children {
        font-weight: 500;
    }
}

.cascader-empty {
    padding: 20px;
    text-align: center;
    color: #999;
}

.custom-slider-button {
    width: 26px;
    color: #fff;
    font-size: 10px;
    line-height: 18px;
    text-align: center;
    background-color: var(--main-blue-);
    border-radius: 100px;
}

// 添加多选弹出层样式
.multi-select-popup {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.multi-select-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #eee;
}

.multi-select-title {
    font-size: 16px;
    font-weight: 600;
}

// 确保字段显示完整
// :deep(.van-field__control) {
//   white-space: nowrap;
//   overflow: hidden;
//   text-overflow: ellipsis;
// }

// 为多选字段添加自定义显示样式
.multiple-select-field {
    :deep(.van-field__control) {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
    }

    .selected-tag {
        display: inline-flex;
        align-items: center;
        background-color: #f0f5ff;
        color: var(--main-blue);
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 12px;
        max-width: 100%;

        .tag-text {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .tag-close {
            margin-left: 4px;
            cursor: pointer;
        }
    }

    .more-count {
        color: #999;
        font-size: 12px;
    }
}
</style>
